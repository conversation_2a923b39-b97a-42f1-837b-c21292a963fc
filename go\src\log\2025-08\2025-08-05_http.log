{"level":"dev.info","ts":"[2025-08-05 09:26:11.917]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858ba8d8946f7c032bf839f","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:26:11.922]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858ba8d8946f7c032bf839f","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0058662,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:26:44.479]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858ba951e17895810212164","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:26:44.485]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858ba951e17895810212164","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0069001,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:27:24.985]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858ba9e8c7cef10d9b11239","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:27:24.989]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858ba9e8c7cef10d9b11239","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.003804,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:28:29.554]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858baad9511b778230f524f","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:28:29.561]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858baad9511b778230f524f","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0077099,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:29:02.964]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bab55c75fa70b061455e","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:29:02.972]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bab55c75fa70b061455e","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0086271,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:29:04.528]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bab5b9b85534acaabd82","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:29:04.530]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bab5b9b85534acaabd82","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0021591,"response_size":67,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:29:31.116]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858babbea6fc8b4ebffa3e0","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:29:32.696]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858babbea6fc8b4ebffa3e0","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":1.5803672,"response_size":96,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:29:58.333]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bac240b7cec854e66694","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:29:58.383]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bac240b7cec854e66694","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.050561,"response_size":279,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:30:19.828]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bac741f66128f90d04f3","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:30:26.558]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bac741f66128f90d04f3","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":6.7301567,"response_size":279,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:31:14.119]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bad3e5ef0670aaaf0cb3","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:31:21.480]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bad3e5ef0670aaaf0cb3","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":7.360975,"response_size":279,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:32:35.576]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bae6dd26c59c47d212e9","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.error","ts":"[2025-08-05 09:32:55.291]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bae6dd26c59c47d212e9","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":19.7154662,"response_size":279,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:33:25.499]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858baf27cc623a848e96582","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.info","ts":"[2025-08-05 09:36:34.093]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bb1e65d8534c70b23a2d","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.441]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bb27cad47d58beffa7a1","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/repayment/paymentcontroller/createRepayment","query":""}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.494]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858bb27cad47d58beffa7a1","method":"POST","url":"/uniapp/repayment/paymentcontroller/createRepayment","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0523692,"response_size":99}
{"level":"dev.info","ts":"[2025-08-05 09:43:54.719]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bb84fd309d38c4707167","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.error","ts":"[2025-08-05 09:43:54.723]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bb84fd309d38c4707167","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0046632,"response_size":96,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:44:53.952]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bb92c7c4fce44b92d099","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.error","ts":"[2025-08-05 09:44:53.960]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bb92c7c4fce44b92d099","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0089241,"response_size":96,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:45:18.360]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bb9876a64cb8b4c51c8b","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.error","ts":"[2025-08-05 09:45:18.363]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1858bb9876a64cb8b4c51c8b","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0031527,"response_size":96,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:45:59.854]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bba21fcf8d00d40675ec","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.info","ts":"[2025-08-05 09:47:19.008]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bbb48dbbaa08e665f87f","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.info","ts":"[2025-08-05 09:48:10.910]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858bbb48dbbaa08e665f87f","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":51.9034906,"response_size":136}
{"level":"dev.info","ts":"[2025-08-05 09:58:20.580]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bc4e969a54b89658fbce","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.info","ts":"[2025-08-05 09:58:41.783]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858bc4e969a54b89658fbce","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":21.2027784,"response_size":136}
{"level":"dev.info","ts":"[2025-08-05 09:59:36.430]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858bc603f8fbb4c1312ab4e","method":"GET","url":"/business/repayment/manager/getPaymentStatus","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1754358122877590963"}
{"level":"dev.info","ts":"[2025-08-05 11:17:40.059]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c0a2bd99bb3c2e7f1687","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:17:40.061]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c0a2bd99bb3c2e7f1687","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0026258,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:20:18.641]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c0c7a9bde8d40afa408f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:20:18.646]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c0c7a9bde8d40afa408f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0066689,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:21:01.517]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c0d1a56d2b501e64e1de","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:22:25.518]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c0e5344cb19844cdd80a","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.557]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c0e5344cb19844cdd80a","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":63.0396041,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:26:39.941]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c120711481105028b82c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:26:39.944]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c120711481105028b82c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0033668,"response_size":3028}
{"level":"dev.info","ts":"[2025-08-05 11:29:20.023]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c145b6b77ea0bf3c867f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:32:14.033]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c16e3a84ef74d0f654f4","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:36:56.608]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c1b0054e86fce3ac99bf","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:36:56.610]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c1b0054e86fce3ac99bf","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.002073,"response_size":3028}
{"level":"dev.info","ts":"[2025-08-05 11:39:20.797]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c1d197a67f48d2dfcdf4","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:39:29.620]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c1d197a67f48d2dfcdf4","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":8.8230203,"response_size":3028}
{"level":"dev.info","ts":"[2025-08-05 11:40:24.587]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c1e071c1f794245bf5c1","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:40:26.908]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c1e071c1f794245bf5c1","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":2.3222697,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:42:54.155]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c20344b32c50e93c48bb","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:42:54.157]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c20344b32c50e93c48bb","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.003673,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:43:01.491]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c204fa1277a8cc62381d","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:43:01.493]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c204fa1277a8cc62381d","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0014975,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:43:07.070]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c2064695ea004f3995f8","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:43:07.071]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858c2064695ea004f3995f8","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0011256,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 11:44:18.849]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c216fce39310329e5c3a","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 11:45:31.795]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858c227f8de1acc42c8a921","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 14:13:13.297]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858ca37338f2e240a6d6903","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 14:13:13.300]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858ca37338f2e240a6d6903","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.002678,"response_size":2566}
{"level":"dev.info","ts":"[2025-08-05 14:13:19.136]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1858ca388f91dbd087454d2c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-05 14:13:19.136]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1858ca388f91dbd087454d2c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0005034,"response_size":2566}
