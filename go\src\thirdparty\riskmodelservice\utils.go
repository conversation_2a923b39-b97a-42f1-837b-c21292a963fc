package riskmodelservice

import (
	"bytes"
	"context"
	"encoding/json"
	"fincore/model"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/log"
	"fmt"
	"io"
	"net/http"
	"time"
)

// fetchThirdPartyDataFromDB 从数据库获取第三方数据
func (s *RiskModelService) fetchThirdPartyDataFromDB(customerID int, result *RiskEvaluationResult) (map[string]interface{}, map[string]interface{}, map[string]interface{}) {
	// 从数据库获取最新的第三方数据
	rawDataService := model.NewRiskRawDataService()
	latestRawData, err := rawDataService.GetLatestByCustomerID(customerID)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Int("customer_id", customerID),
			log.String("action", "get_third_party_data_from_db_failed"),
		).Error("从数据库获取第三方数据失败")
		return nil, nil, nil
	}

	if latestRawData == nil {
		s.logger.WithFields(
			log.Int("customer_id", customerID),
			log.String("action", "no_third_party_data_in_db"),
		).Warn("数据库中没有找到第三方数据")
		return nil, nil, nil
	}

	var tanZhenData, leidaData, zwscData map[string]interface{}

	// 解析tan_zhen_c数据
	if latestRawData.TanZhenCResponse != "" {
		if err := json.Unmarshal([]byte(latestRawData.TanZhenCResponse), &tanZhenData); err != nil {
			s.logger.WithError(err).WithFields(
				log.String("action", "parse_tan_zhen_data_failed"),
			).Error("解析tan_zhen_c数据失败")
			tanZhenData = nil
		} else {
			result.ThirdPartyResults["tanZhen"] = tanZhenData
		}
	}

	// 解析leida_v4数据
	if latestRawData.LeidaV4Response != "" {
		if err := json.Unmarshal([]byte(latestRawData.LeidaV4Response), &leidaData); err != nil {
			s.logger.WithError(err).WithFields(
				log.String("action", "parse_leida_data_failed"),
			).Error("解析leida_v4数据失败")
			leidaData = nil
		} else {
			result.ThirdPartyResults["leida"] = leidaData
		}
	}

	// 解析zwsc数据
	if latestRawData.ZwscResponse != "" {
		if err := json.Unmarshal([]byte(latestRawData.ZwscResponse), &zwscData); err != nil {
			s.logger.WithError(err).WithFields(
				log.String("action", "parse_zwsc_data_failed"),
			).Error("解析zwsc数据失败")
			zwscData = nil
		} else {
			result.ThirdPartyResults["zwsc"] = zwscData
		}
	}

	s.logger.WithFields(
		log.Int("customer_id", customerID),
		log.Bool("has_tan_zhen", tanZhenData != nil),
		log.Bool("has_leida", leidaData != nil),
		log.Bool("has_zwsc", zwscData != nil),
		log.String("action", "fetch_third_party_data_from_db_success"),
	).Info("从数据库获取第三方数据成功")

	return tanZhenData, leidaData, zwscData
}

// checkBlacklistFromThirdParty 使用第三方服务检查黑名单
func (s *RiskModelService) checkBlacklistFromThirdParty(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) *RiskEvaluationResult {
	xdhmdThirdPartyData, err := s.getThirdPartyData(thirdPartyService, "xdhmd", riskRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", "xdhmd"),
			log.String("action", "get_blacklist_data"),
		).Error("获取黑名单数据失败")
		return nil // 黑名单数据获取失败，继续后续流程
	}

	// 检查黑名单状态
	if blackList, exists := xdhmdThirdPartyData["black_list"]; exists {
		if blackListStr, ok := blackList.(string); ok && blackListStr == "1" {
			// 命中黑名单，返回拒绝结果
			s.logger.WithFields(
				log.String("blacklist_status", blackListStr),
				log.String("action", "blacklist_rejected"),
				log.String("failure_type", FailureTypeExternalBlacklist),
			).Warn("用户命中外部黑名单，直接拒绝")
			result.FinalResult = model.REJECTED
			result.FailureType = FailureTypeExternalBlacklist
			result.FailureReason = "外部黑名单"
			return result
		}
	}

	// 将黑名单数据保存到结果中
	result.ThirdPartyResults["blacklist_data"] = xdhmdThirdPartyData
	return nil
}

// checkNetworkDurationFromDB 从数据库数据检查在网时长
func (s *RiskModelService) checkNetworkDurationFromDB(zwscData map[string]interface{}, result *RiskEvaluationResult) *RiskEvaluationResult {
	if zwscData == nil {
		s.logger.WithFields(
			log.String("action", "no_zwsc_data_for_network_duration_check"),
		).Warn("没有zwsc数据进行在网时长检查")
		return nil
	}

	// 检查在网时长字段
	if zwscTime, exists := zwscData["zwsc_time"]; exists {
		if zwscTimeFloat, ok := zwscTime.(float64); ok {
			if zwscTimeFloat < 3 {
				s.logger.WithFields(
					log.Float64("zwsc_time", zwscTimeFloat),
					log.String("action", "network_duration_insufficient"),
				).Warn("在网时长不足")
				result.FinalResult = model.REJECTED
				result.FailureType = FailureTypeNetworkDuration
				result.FailureReason = fmt.Sprintf("在网时长不足: %.1f个月", zwscTimeFloat)
				return result
			}
		}
	}

	return nil
}

// createMockApprovedResult 创建模拟通过结果
func (s *RiskModelService) createMockApprovedResult() *RiskEvaluationResult {
	return &RiskEvaluationResult{
		FinalResult: model.APPROVED,
		FinalScore:  model.MaxRiskScore,
		CreditLimit: &CreditLimitResponse{
			CreditLimit: "10000",
			Code:        200,
			Message:     "success",
		},
		ThirdPartyResults: make(map[string]interface{}),
		ModelResults:      make(map[string]interface{}),
	}
}

// recordError 记录错误到结果中
func (s *RiskModelService) recordError(result *RiskEvaluationResult, errorType, errorMsg string) {
	result.ThirdPartyResults[errorType] = errorMsg
	result.ModelResults[errorType] = errorMsg
}

// createModelHTTPClient 创建HTTP客户端
func createModelHTTPClient() *http.Client {
	return &http.Client{
		Timeout: 30 * time.Second,
	}
}

// makeRequest 发送HTTP请求的通用方法
func (s *RiskModelService) makeRequest(ctx context.Context, method, url string, request interface{}, response interface{}) error {
	// 序列化请求参数
	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	err = json.Unmarshal(body, response)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	return nil
}

// combineThirdPartyData 合并第三方数据
func (s *RiskModelService) combineThirdPartyData(tanZhenData, leidaData map[string]interface{}) map[string]interface{} {
	combinedData := make(map[string]interface{})

	// 添加tan_zhen_c数据
	if tanZhenData != nil {
		combinedData["tan_zhen_c"] = tanZhenData
	}

	// 添加leida_v4数据
	if leidaData != nil {
		combinedData["leida_v4"] = leidaData
	}

	// 添加合并标识
	combinedData["data_source"] = "combined_thirdparty"
	combinedData["merge_time"] = time.Now().Format("2006-01-02 15:04:05")

	return combinedData
}
