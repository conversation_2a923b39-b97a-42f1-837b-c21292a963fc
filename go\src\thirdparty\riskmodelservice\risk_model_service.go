package riskmodelservice

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/log"
)

// FailureType 失败类型枚举
const (
	FailureTypeInternalBlacklist = "internal_blacklist" // 内部黑名单
	FailureTypeExternalBlacklist = "external_blacklist" // 外部黑名单
	FailureTypeRiskPolicy        = "risk_policy"        // 风控准入策略
	FailureTypeRiskScore         = "risk_score"         // 风险评分
	FailureTypeThirdPartyData    = "third_party_data"   // 第三方数据
	FailureTypeModelCall         = "model_call"         // 模型调用失败
	FailureTypeModelPolicyCall   = "model_policy_call"  // 模型策略调用失败
	FailureTypeFeatureProcess    = "feature_process"    // 特征处理失败
	FailureTypeNetworkDuration   = "network_duration"   // 在网时长不足
)

// RiskModelConfig 风控模型配置
type RiskModelConfig struct {
	BaseURL string        `json:"base_url"`
	Timeout time.Duration `json:"timeout"`
}

// RCSRequest RCS接口请求参数
type RCSRequest struct {
	LeidaV4  interface{} `json:"leida_v4"`
	TanZhenC interface{} `json:"tan_zhen_c"`
}

// RCSResponse RCS接口响应
type RCSResponse struct {
	RiskResult int    `json:"riskResult"` // 0:通过，1:尽调，2:拒绝
	Message    string `json:"message"`
	Code       int    `json:"code"` // 200:成功，400:失败
	DataInput  string `json:"dataInput"`
}

// ModelScoreRequest 模型评分接口请求参数
type ModelScoreRequest struct {
	ProductID string                 `json:"productId"`
	Data      map[string]interface{} `json:"data"`
}

// ModelScoreResponse 模型评分接口响应
type ModelScoreResponse struct {
	Message    string  `json:"message"`
	Code       int     `json:"code"` // 200:成功，400:失败
	ModelScore float64 `json:"modelScore"`
}

// CreditLimitRequest 授信额度接口请求参数
type CreditLimitRequest struct {
	ModelScore float64 `json:"modelScore"`
	OtherScore float64 `json:"otherScore"`
}

// CreditLimitResponse 授信额度接口响应
type CreditLimitResponse struct {
	Message     string `json:"message"`
	Code        int    `json:"code"` // 200:成功，400:失败
	DataInput   string `json:"dataInput"`
	CreditLimit string `json:"creditLimit"`
}

// ProductData 产品数据结构
type ProductData struct {
	Code        string                 `json:"code"`
	Data        map[string]interface{} `json:"data"`
	Message     string                 `json:"message"`
	RequestID   string                 `json:"requestId"`
	RequestTime string                 `json:"requestTime"`
}

// RiskModelService 风控模型服务
type RiskModelService struct {
	config *RiskModelConfig
	client *http.Client
	logger *log.Logger
}

// NewRiskModelService 创建新的风控模型服务
func NewRiskModelService(ctx context.Context) *RiskModelService {
	return &RiskModelService{
		config: GetModelConfigFromGlobal(),
		client: createModelHTTPClient(),
		logger: log.Risk().WithContext(ctx),
	}
}

// GetModelConfigFromGlobal 从全局配置中获取风控模型服务配置
func GetModelConfigFromGlobal() *RiskModelConfig {
	if global.App == nil {
		panic("全局配置未初始化，请先初始化配置")
	}

	modelConfig := global.App.Config.RiskModel
	return &RiskModelConfig{
		BaseURL: modelConfig.BaseURL,
		Timeout: time.Duration(modelConfig.Timeout) * time.Second,
	}
}

// RiskResultResponse 风控准入策略响应
type RiskResultResponse struct {
	RiskResult  int      `json:"riskResult"` // 0:通过，1:尽调，2:拒绝
	Message     string   `json:"message"`
	Code        int      `json:"code"` // 200:成功，400:失败
	FailedRules []string `json:"failedRules"`
}

// RiskEvaluationResult 风控评估结果
type RiskEvaluationResult struct {
	ThirdPartyResults map[string]interface{} `json:"thirdPartyResults"`
	ModelResults      map[string]interface{} `json:"modelResults"`
	CreditLimit       *CreditLimitResponse   `json:"creditLimit"`
	FinalScore        float64                `json:"finalScore"`
	FinalResult       int                    `json:"finalResult"`
	FailureType       string                 `json:"failureType,omitempty"`
	FailureReason     string                 `json:"failureReason,omitempty"`
}

// ProcessRiskEvaluation 处理风控评估流程
// ProcessRiskEvaluationWithCustomer 使用客户信息进行风控评估
func (s *RiskModelService) ProcessRiskEvaluationWithCustomer(ctx context.Context, customerID int, riskRequest *riskthirdparty.RiskRequest) (*RiskEvaluationResult, error) {
	s.logger.WithFields(
		log.String("action", "start_risk_evaluation_with_customer"),
		log.Int("customer_id", customerID),
	).Info("开始使用客户信息进行风控评估")

	result := &RiskEvaluationResult{
		ThirdPartyResults: make(map[string]interface{}),
		ModelResults:      make(map[string]interface{}),
	}

	// 创建第三方服务实例
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()

	// 从数据库获取第三方数据
	tanZhenData, leidaData, zwscData := s.fetchThirdPartyDataFromDB(customerID, result)
	if tanZhenData == nil && leidaData == nil {
		s.logger.WithFields(
			log.String("action", "third_party_data_failed"),
			log.String("failure_type", FailureTypeThirdPartyData),
		).Error("从数据库获取第三方数据失败，直接拒绝")
		result.FinalResult = model.REJECTED
		result.FailureType = FailureTypeThirdPartyData
		result.FailureReason = "从数据库获取第三方数据失败"
		return result, nil
	}

	// 检查黑名单（使用第三方服务获取xdhmd数据）
	if blacklistResult := s.checkBlacklistFromThirdParty(thirdPartyService, riskRequest, result); blacklistResult != nil {
		return blacklistResult, nil
	}

	// 检查在网时长（使用数据库中的zwsc数据）
	if networkDurationResult := s.checkNetworkDurationFromDB(zwscData, result); networkDurationResult != nil {
		return networkDurationResult, nil
	}

	// 处理特征数据并调用模型
	result, err := s.processAndEvaluateRisk(ctx, result, tanZhenData, leidaData)
	if err != nil {
		return nil, fmt.Errorf("处理特征数据并调用模型失败: %w", err)
	}

	return result, nil
}

// processRiskEvaluationInternal 内部风控评估处理逻辑
func (s *RiskModelService) ProcessRiskEvaluationInternal(ctx context.Context, riskRequest *riskthirdparty.RiskRequest) (*RiskEvaluationResult, error) {
	s.logger.WithFields(
		log.String("action", "start_risk_evaluation"),
	).Info("开始风控评估")

	result := &RiskEvaluationResult{
		ThirdPartyResults: make(map[string]interface{}),
		ModelResults:      make(map[string]interface{}),
	}

	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()

	// 1. 黑名单检查
	if blacklistResult := s.checkExternalBlacklist(thirdPartyService, riskRequest, result); blacklistResult != nil {
		return blacklistResult, nil
	}

	// 2. 在网时长检查
	if networkDurationResult := s.checkNetworkDuration(thirdPartyService, riskRequest, result); networkDurationResult != nil {
		return networkDurationResult, nil
	}

	// 3. 获取第三方数据
	tanZhenData, leidaData := s.fetchThirdPartyData(thirdPartyService, riskRequest, result)
	if tanZhenData == nil && leidaData == nil {
		//构造返回数据
		s.logger.WithFields(
			log.String("action", "third_party_data_failed"),
			log.String("failure_type", FailureTypeThirdPartyData),
		).Error("获取第三方数据失败，直接拒绝")
		result.FinalResult = model.REJECTED
		result.FailureType = FailureTypeThirdPartyData
		result.FailureReason = "获取第三方数据失败"
		return result, nil
	}

	// 4. 处理特征数据并调用模型
	result, err := s.processAndEvaluateRisk(ctx, result, tanZhenData, leidaData)
	if err != nil {
		return nil, fmt.Errorf("处理特征数据并调用模型失败: %w", err)
	}

	return result, nil
}

// checkExternalBlacklist 检查外部黑名单
func (s *RiskModelService) checkExternalBlacklist(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) *RiskEvaluationResult {
	xdhmdThirdPartyData, err := s.getThirdPartyData(thirdPartyService, "xdhmd", riskRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", "xdhmd"),
			log.String("action", "get_blacklist_data"),
		).Error("获取黑名单数据失败")
		return nil // 黑名单数据获取失败，继续后续流程
	}

	// 检查黑名单状态
	if blackList, exists := xdhmdThirdPartyData["black_list"]; exists {
		if blackListStr, ok := blackList.(string); ok && blackListStr == "1" {
			// 命中黑名单，返回拒绝结果
			s.logger.WithFields(
				log.String("blacklist_status", blackListStr),
				log.String("action", "blacklist_rejected"),
				log.String("failure_type", FailureTypeExternalBlacklist),
			).Warn("用户命中外部黑名单，直接拒绝")
			return s.createBlacklistRejectedResult(result, xdhmdThirdPartyData)
		}
	}

	// 将黑名单数据保存到结果中
	result.ThirdPartyResults["blacklist_data"] = xdhmdThirdPartyData
	return nil
}

// checkNetworkDuration 检查在网时长
func (s *RiskModelService) checkNetworkDuration(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) *RiskEvaluationResult {
	zwscThirdPartyData, err := s.getThirdPartyData(thirdPartyService, "zwsc", riskRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", "zwsc"),
			log.String("action", "get_network_duration_data"),
		).Error("获取在网时长数据失败")
		return nil // 在网时长数据获取失败，继续后续流程
	}

	// 检查查询结果是否成功
	if queryResult, exists := zwscThirdPartyData["queryResult"]; exists {
		if queryResultStr, ok := queryResult.(string); ok && queryResultStr == "1" {
			// 查得数据，检查在网时长
			if minValue, exists := zwscThirdPartyData["min"]; exists {
				// max为null表示两年以上，不需要拒绝
				if minValue != nil {
					if minInt, ok := minValue.(float64); ok {
						// 在网时长小于6个月的用户拒掉
						if minInt <= 6 {
							s.logger.WithFields(
								log.Float64("network_duration_months", minInt),
								log.String("action", "network_duration_rejected"),
								log.String("failure_type", FailureTypeNetworkDuration),
							).Warn("用户在网时长不足6个月，直接拒绝")
							return s.createNetworkDurationRejectedResult(result, zwscThirdPartyData)
						}
					}
				}
			}
		}
	}

	// 将在网时长数据保存到结果中
	result.ThirdPartyResults["network_duration_data"] = zwscThirdPartyData
	return nil
}

// createBlacklistRejectedResult 创建黑名单拒绝结果
func (s *RiskModelService) createBlacklistRejectedResult(result *RiskEvaluationResult, blacklistData map[string]interface{}) *RiskEvaluationResult {
	result.FinalResult = model.REJECTED
	result.FinalScore = 0.0
	result.ThirdPartyResults["blacklist_data"] = blacklistData
	result.FailureType = FailureTypeExternalBlacklist

	if blacklistBytes, err := json.Marshal(blacklistData); err == nil {
		result.FailureReason = string(blacklistBytes)
	} else {
		result.FailureReason = "外部黑名单数据序列化失败"
	}

	return result
}

// createNetworkDurationRejectedResult 创建在网时长拒绝结果
func (s *RiskModelService) createNetworkDurationRejectedResult(result *RiskEvaluationResult, networkDurationData map[string]interface{}) *RiskEvaluationResult {
	result.FinalResult = model.REJECTED
	result.FinalScore = 0.0
	result.ThirdPartyResults["network_duration_data"] = networkDurationData
	result.FailureType = FailureTypeNetworkDuration

	if networkDurationBytes, err := json.Marshal(networkDurationData); err == nil {
		result.FailureReason = string(networkDurationBytes)
	} else {
		result.FailureReason = "在网时长数据序列化失败"
	}

	return result
}

// fetchThirdPartyData 获取第三方数据（保留原有逻辑作为备用）
func (s *RiskModelService) fetchThirdPartyData(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) (map[string]interface{}, map[string]interface{}) {
	tanZhenData, err := s.getThirdPartyData(thirdPartyService, "tan_zhen_c", riskRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", "tan_zhen_c"),
			log.String("action", "get_third_party_data"),
		).Error("获取tan_zhen_c第三方数据失败")
		tanZhenData = nil
	} else {
		result.ThirdPartyResults["tanZhen"] = tanZhenData
	}

	// 获取leida_v4数据
	leidaData, err := s.getThirdPartyData(thirdPartyService, "leida_v4", riskRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", "leida_v4"),
			log.String("action", "get_third_party_data"),
		).Error("获取leida_v4第三方数据失败")
		leidaData = nil
	} else {
		result.ThirdPartyResults["leida"] = leidaData
	}

	return tanZhenData, leidaData
}

// processAndEvaluateRisk 处理特征数据并调用风控模型
func (s *RiskModelService) processAndEvaluateRisk(ctx context.Context, result *RiskEvaluationResult, tanZhenData, leidaData map[string]interface{}) (*RiskEvaluationResult, error) {
	// 1. 特征处理
	processedData, err := s.processFeatureData(ctx, leidaData, tanZhenData)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("action", "process_feature_data"),
			log.String("failure_type", FailureTypeFeatureProcess),
		).Error("特征处理失败")
		s.recordError(result, "feature_process_error", err.Error())
		result.FailureType = FailureTypeFeatureProcess
		result.FailureReason = err.Error()
		return result, fmt.Errorf("特征处理失败: %w", err)
	} else {
		result.ModelResults["processed_features"] = processedData
	}

	// 2. 调用风控模型接口
	riskResult, modelScore, err := s.callRiskModels(ctx, processedData, result)
	if err != nil {
		return result, fmt.Errorf("调用风控模型接口失败: %w", err)
	}

	// 3. 计算最终结果
	if err := s.calculateFinalResult(result, riskResult, modelScore); err != nil {
		return result, fmt.Errorf("计算最终结果失败: %w", err)
	}

	s.logger.WithFields(
		log.Float64("final_score", result.FinalScore),
		log.Int("final_result", result.FinalResult),
		log.Bool("has_credit_limit", result.CreditLimit != nil),
		log.String("action", "risk_evaluation_completed"),
	).Info("风控评估完成")

	return result, nil
}

// callRiskModels 调用风控模型接口
func (s *RiskModelService) callRiskModels(ctx context.Context, processedData map[string]interface{}, result *RiskEvaluationResult) (*RiskResultResponse, *ModelScoreResponse, error) {
	// 调用风控准入策略接口
	riskResult, err := s.callRiskResultAPI(ctx, processedData)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("action", "call_risk_result_api"),
			log.String("failure_type", FailureTypeModelPolicyCall),
		).Error("风控准入策略调用失败")
		s.recordError(result, "risk_result_error", err.Error())
		result.FailureType = FailureTypeModelPolicyCall
		result.FailureReason = err.Error()
		return nil, nil, fmt.Errorf("风控准入策略调用失败: %w", err)
	} else {
		s.logger.WithFields(
			log.Int("risk_result", riskResult.RiskResult),
			log.Int("response_code", riskResult.Code),
			log.String("action", "call_risk_result_api_success"),
		).Info("风控准入策略调用成功")
		result.ModelResults["risk_result"] = riskResult
	}

	// 调用风控模型评分接口
	modelScore, err := s.callModelScoreAPI(ctx, processedData)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("action", "call_model_score_api"),
			log.String("failure_type", FailureTypeModelCall),
		).Error("风控模型评分调用失败")
		s.recordError(result, "model_score_error", err.Error())
		if result.FailureType == "" {
			result.FailureType = FailureTypeModelCall
			result.FailureReason = err.Error()
		}
		return riskResult, nil, fmt.Errorf("风控模型评分调用失败: %w", err)
	} else {
		s.logger.WithFields(
			log.Float64("model_score", modelScore.ModelScore),
			log.Int("response_code", modelScore.Code),
			log.String("action", "call_model_score_api_success"),
		).Info("风控模型评分调用成功")
		result.ModelResults["model_score"] = modelScore
	}

	return riskResult, modelScore, nil
}

// calculateFinalResult 计算最终评分和结果
func (s *RiskModelService) calculateFinalResult(result *RiskEvaluationResult, riskResult *RiskResultResponse, modelScore *ModelScoreResponse) error {
	// 计算最终评分
	finalScore := 0.0
	if modelScore != nil && modelScore.Code == 200 {
		finalScore = modelScore.ModelScore
	} else {
		s.logger.WithFields(
			log.Float64("default_score", finalScore),
			log.String("action", "use_default_score"),
			log.String("failure_type", FailureTypeRiskScore),
		).Warn("模型评分失败，使用默认评分")
		// 如果模型评分失败且之前没有设置失败类型，则设置为风控评分失败
		if result.FailureType == "" {
			result.FailureType = FailureTypeRiskScore
			result.FailureReason = "风控模型评分失败"
		}
	}
	result.FinalScore = finalScore

	// 确定最终结果
	if riskResult != nil && riskResult.Code == 200 {
		result.FinalResult = riskResult.RiskResult
		// 如果风控准入策略拒绝，记录失败原因
		if riskResult.RiskResult == model.REJECTED && len(riskResult.FailedRules) > 0 {
			result.FailureType = FailureTypeRiskPolicy
			if failedRulesBytes, err := json.Marshal(riskResult.FailedRules); err == nil {
				result.FailureReason = string(failedRulesBytes)
			} else {
				result.FailureReason = "风控准入策略失败规则序列化失败"
			}
		}
	} else {
		result.FinalResult = model.CALLRISKMODELFAILED
		// 如果风控准入策略调用失败且之前没有设置失败类型，则设置为模型调用失败
		if result.FailureType == "" {
			result.FailureType = FailureTypeModelCall
			result.FailureReason = "风控准入策略调用失败"
		}
	}

	return nil
}

// getThirdPartyData 获取第三方风控数据
func (s *RiskModelService) getThirdPartyData(thirdPartyService *riskthirdparty.RiskThirdPartyService, productID string, request *riskthirdparty.RiskRequest) (map[string]interface{}, error) {
	// 临时修改配置中的产品ID
	originalConfig := thirdPartyService.GetConfig()
	modifiedConfig := *originalConfig
	modifiedConfig.ProductID = productID
	thirdPartyService.SetConfig(&modifiedConfig)

	// 调用第三方接口
	response, err := thirdPartyService.QueryRiskData(request)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.String("product_id", productID),
			log.String("action", "query_third_party_risk_data"),
		).Error("调用第三方风控接口失败")
		return nil, fmt.Errorf("调用第三方风控接口失败: %v", err)
	}

	// 恢复原始配置
	thirdPartyService.SetConfig(originalConfig)

	return response.Data, nil
}

// processFeatureData 统一调用特征处理接口
func (s *RiskModelService) processFeatureData(ctx context.Context, leidaData, tanZhenData map[string]interface{}) (map[string]interface{}, error) {
	// 构建统一特征处理请求
	featureRequest := map[string]interface{}{
		"leida_v4":   leidaData,
		"tan_zhen_c": tanZhenData,
	}

	// 调用统一特征处理接口
	url := s.config.BaseURL + "/feature_process"
	var response map[string]interface{}
	err := s.makeRequest(ctx, "POST", url, featureRequest, &response)
	if err != nil {
		return nil, fmt.Errorf("调用特征处理接口失败: %v", err)
	}

	// 从响应的data字段中提取处理后的数据
	if data, ok := response["data"].(map[string]interface{}); ok {
		return data, nil
	}

	return response, nil
}

// callRiskResultAPI 调用风控准入策略接口
func (s *RiskModelService) callRiskResultAPI(ctx context.Context, processedData map[string]interface{}) (*RiskResultResponse, error) {
	//mock 返回数据 RiskResult 随机返回 0~2
	// riskResult := rand.Intn(3)
	// return &RiskResultResponse{
	// 	Code:       200,
	// 	Message:    "success",
	// 	RiskResult: riskResult,
	// }, nil

	// 调用风控准入策略接口
	url := s.config.BaseURL + "/product_1/riskResult"
	var response RiskResultResponse
	err := s.makeRequest(ctx, "POST", url, processedData, &response)
	if err != nil {
		return nil, fmt.Errorf("调用风控准入策略接口失败: %v", err)
	}

	return &response, nil
}

// callModelScoreAPI 调用风控模型评分接口
func (s *RiskModelService) callModelScoreAPI(ctx context.Context, processedData map[string]interface{}) (*ModelScoreResponse, error) {
	//mock 返回数据 ModelScore 随机返回 0~100 浮点树
	// modelScore := rand.Float64() * 100
	// return &ModelScoreResponse{
	// 	Code:       200,
	// 	Message:    "success",
	// 	ModelScore: modelScore,
	// }, nil

	// 调用风控模型评分接口
	url := s.config.BaseURL + "/product_1/modelScore"
	var response ModelScoreResponse
	err := s.makeRequest(ctx, "POST", url, processedData, &response)
	if err != nil {
		return nil, fmt.Errorf("调用风控模型评分接口失败: %v", err)
	}

	return &response, nil
}
