openapi: 3.0.0
info:
  title: FinCore 渠道统计 API
  description: 渠道统计控制器的完整API接口文档
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8108
    description: 渠道统计API服务器（开发环境）
  - url: https://api.fincore.com
    description: 渠道统计API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    ChannelStatisticsItem:
      type: object
      description: 渠道统计数据项
      properties:
        id:
          type: integer
          description: 统计记录ID
          example: 1
        channel_id:
          type: integer
          description: 渠道ID
          example: 3
        channel_name:
          type: string
          description: 渠道名称
          example: "test"
        channel_code:
          type: string
          description: 渠道编码
          example: "NZKMYUHLPO"
        new_customer_reg_num:
          type: integer
          description: 新用户注册数
          example: 5
        real_name_num:
          type: integer
          description: 实名认证数
          example: 4
        number_of_transactions:
          type: integer
          description: 交易笔数
          example: 2
        created_at:
          type: string
          description: 统计时间（记录创建时间）
          example: "2024-08-04 12:00:00"
      required:
        - id
        - channel_id
        - channel_name
        - channel_code
        - new_customer_reg_num
        - real_name_num
        - number_of_transactions
        - created_at

    ChannelStatisticsPaginationResponse:
      type: object
      description: 渠道统计分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 5
        page:
          type: integer
          description: 当前页码
          example: 1
        page_size:
          type: integer
          description: 每页数量
          example: 10
        total_pages:
          type: integer
          description: 总页数
          example: 1
        has_next:
          type: boolean
          description: 是否有下一页
          example: false
        has_prev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 渠道统计数据列表
          items:
            $ref: '#/components/schemas/ChannelStatisticsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    ChannelStatisticsListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChannelStatisticsPaginationResponse'

paths:
  /business/statistics/statisticscontroller/getChannelStatistics:
    get:
      tags:
        - 渠道统计
      summary: 获取渠道统计列表
      description: |
        获取渠道维度的统计数据列表，支持分页和日期筛选。
        
        **业务说明：**
        - 查询channel_statistics表数据并关联channel表获取渠道信息
        - 支持按日期筛选统计数据
        - 返回数据包含：渠道信息、新用户注册数、实名认证数、交易笔数等
        - 按统计时间倒序排列
        
        **权限要求：**
        - 需要登录用户权限
        - 需要渠道统计查看权限
      parameters:
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: date
          in: query
          description: 统计日期筛选（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-04"
      responses:
        '200':
          description: 获取渠道统计列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelStatisticsListResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 3
                      page: 1
                      pageSize: 10
                      totalPages: 1
                      hasNext: false
                      hasPrev: false
                      data:
                        - id: 3
                          channel_id: 3
                          channel_name: "test"
                          channel_code: "NZKMYUHLPO"
                          new_customer_reg_num: 5
                          real_name_num: 4
                          number_of_transactions: 2
                          created_at: "2024-08-04 12:00:00"
                        - id: 2
                          channel_id: 2
                          channel_name: "公众号"
                          channel_code: "GZH8F5N3P7"
                          new_customer_reg_num: 15
                          real_name_num: 12
                          number_of_transactions: 8
                          created_at: "2024-08-04 11:00:00"
                        - id: 1
                          channel_id: 1
                          channel_name: "fincore"
                          channel_code: "HS2K9X7Q1M"
                          new_customer_reg_num: 10
                          real_name_num: 8
                          number_of_transactions: 5
                          created_at: "2024-08-04 10:00:00"
                    exdata: null
                    token: ""
                    time: 1754292247
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 0
                      page: 1
                      pageSize: 10
                      totalPages: 0
                      hasNext: false
                      hasPrev: false
                      data: []
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_page:
                  summary: 页码参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取渠道统计列表失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247

tags:
  - name: 渠道统计
    description: 渠道统计相关接口
