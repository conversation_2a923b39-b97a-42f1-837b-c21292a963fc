

[31m2025/08/05 09:26:11 [Recovery] 2025/08/05 - 09:26:11 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x16e5704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x16e67e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x16e6224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1423d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x12ed83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x12ec105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1424145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14252b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x12ed83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x12ec105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x12da2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x12ea1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x12e9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x10028d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xfcf794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xb520e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:26:44 [Recovery] 2025/08/05 - 09:26:44 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x1845704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x18467e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1846224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1583d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x144d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x144c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1584145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x15852b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x144d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x144c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x144a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1449cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x11628d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x112f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xcb20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:27:24 [Recovery] 2025/08/05 - 09:27:24 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x1845704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x18467e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1846224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1583d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x144d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x144c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1584145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x15852b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x144d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x144c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x143a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x144a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1449cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x11628d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x112f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xcb20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:28:29 [Recovery] 2025/08/05 - 09:28:29 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x1235704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x12367e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1236224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xf73d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xf74145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xf752b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe3d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe3c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe3a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe39cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xb528d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xb1f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x6a20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:29:02 [Recovery] 2025/08/05 - 09:29:02 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x12b5704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x12b67e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x12b6224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xff3d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xebd83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xebc105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xff4145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xff52b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xebd83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xebc105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xeba1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xeb9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xbd28d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xb9f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x7220e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:29:04 [Recovery] 2025/08/05 - 09:29:04 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token 不存在
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:98 (0x12b5704)
	JwtVerify: panic("token 不存在")
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:19 (0x12b67e8)
	ValidityAPi.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x12b6224)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xff3d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xebd83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xebc105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xff4145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xff52b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xebd83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xebc105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xeaa2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xeba1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xeb9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xbd28d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xb9f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x7220e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:29:32 [Recovery] 2025/08/05 - 09:29:32 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:111 (0x16a5616)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/business/repayment/service.go:420 (0x1910c99)
	(*PaymentService).CreateRepayment: claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
D:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83 (0x1d9c9a7)
	(*PaymentController).CreateRepayment: result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xc260c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xc25072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x16034c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x16a5d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x13e3d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x12ad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x12ac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x13e4145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x13e52b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x12ad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x12ac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x129a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x12aa1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x12a9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xfc28d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xf8f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xb120e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:29:58 [Recovery] 2025/08/05 - 09:29:58 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0xd8547d)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0xd9d1ab)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/utils/gform/engin.go:114 (0x176e86b)
	(*Engin).GetExecuteDB: if c.dbs.masterSize == 0 {
D:/work/code/fincore/go/src/utils/gform/session.go:38 (0x177bac4)
	NewSession: s.master = e.GetExecuteDB()
D:/work/code/fincore/go/src/utils/gform/orm.go:22 (0x1770e84)
	NewOrm: orm.SetISession(NewSession(e))
D:/work/code/fincore/go/src/utils/gform/engin.go:243 (0x176f809)
	(*Engin).NewOrm: return NewOrm(c)
D:/work/code/fincore/go/src/model/model.go:58 (0x1924c32)
	DB: orm := engin.NewOrm()
D:/work/code/fincore/go/src/model/business_repayment_bills.go:231 (0x1916386)
	(*BusinessRepaymentBillsService).GetBillByID: data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").Where("id", billID).First()
D:/work/code/fincore/go/src/app/business/repayment/service.go:432 (0x1bc0df0)
	(*PaymentService).createRepaymentInternal: bill, err := s.billModel.GetBillByID(billID)
D:/work/code/fincore/go/src/app/business/repayment/service.go:426 (0x1bc0ca4)
	(*PaymentService).CreateRepayment: return s.createRepaymentInternal(int(123), billID, bankCardID, false)
D:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83 (0x204c927)
	(*PaymentController).CreateRepayment: result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xed60c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xed5072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x18b34c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1955d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1693d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1694145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x16952b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x155a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1559cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x12728d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x123f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xdc20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:30:26 [Recovery] 2025/08/05 - 09:30:26 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0xd8547d)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0xd9d1ab)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/utils/gform/engin.go:114 (0x176e86b)
	(*Engin).GetExecuteDB: if c.dbs.masterSize == 0 {
D:/work/code/fincore/go/src/utils/gform/session.go:38 (0x177bac4)
	NewSession: s.master = e.GetExecuteDB()
D:/work/code/fincore/go/src/utils/gform/orm.go:22 (0x1770e84)
	NewOrm: orm.SetISession(NewSession(e))
D:/work/code/fincore/go/src/utils/gform/engin.go:243 (0x176f809)
	(*Engin).NewOrm: return NewOrm(c)
D:/work/code/fincore/go/src/model/model.go:58 (0x1924c32)
	DB: orm := engin.NewOrm()
D:/work/code/fincore/go/src/model/business_repayment_bills.go:231 (0x1916386)
	(*BusinessRepaymentBillsService).GetBillByID: data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").Where("id", billID).First()
D:/work/code/fincore/go/src/app/business/repayment/service.go:432 (0x1bc0df0)
	(*PaymentService).createRepaymentInternal: bill, err := s.billModel.GetBillByID(billID)
D:/work/code/fincore/go/src/app/business/repayment/service.go:426 (0x1bc0ca4)
	(*PaymentService).CreateRepayment: return s.createRepaymentInternal(int(123), billID, bankCardID, false)
D:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83 (0x204c927)
	(*PaymentController).CreateRepayment: result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xed60c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xed5072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x18b34c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1955d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1693d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1694145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x16952b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x155a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1559cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x12728d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x123f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xdc20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:31:21 [Recovery] 2025/08/05 - 09:31:21 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0xd8547d)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0xd9d1ab)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/utils/gform/engin.go:114 (0x176e86b)
	(*Engin).GetExecuteDB: if c.dbs.masterSize == 0 {
D:/work/code/fincore/go/src/utils/gform/session.go:38 (0x177bac4)
	NewSession: s.master = e.GetExecuteDB()
D:/work/code/fincore/go/src/utils/gform/orm.go:22 (0x1770e84)
	NewOrm: orm.SetISession(NewSession(e))
D:/work/code/fincore/go/src/utils/gform/engin.go:243 (0x176f809)
	(*Engin).NewOrm: return NewOrm(c)
D:/work/code/fincore/go/src/model/model.go:58 (0x1924c32)
	DB: orm := engin.NewOrm()
D:/work/code/fincore/go/src/model/business_repayment_bills.go:231 (0x1916386)
	(*BusinessRepaymentBillsService).GetBillByID: data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").Where("id", billID).First()
D:/work/code/fincore/go/src/app/business/repayment/service.go:432 (0x1bc0df0)
	(*PaymentService).createRepaymentInternal: bill, err := s.billModel.GetBillByID(billID)
D:/work/code/fincore/go/src/app/business/repayment/service.go:426 (0x1bc0ca4)
	(*PaymentService).CreateRepayment: return s.createRepaymentInternal(int(123), billID, bankCardID, false)
D:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83 (0x204c927)
	(*PaymentController).CreateRepayment: result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xed60c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xed5072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x18b34c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1955d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1693d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1694145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x16952b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x155a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1559cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x12728d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x123f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xdc20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:32:55 [Recovery] 2025/08/05 - 09:32:55 panic recovered:
POST /uniapp/repayment/paymentcontroller/createRepayment HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 46
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0xd8547d)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0xd9d1ab)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/utils/gform/engin.go:114 (0x176e86b)
	(*Engin).GetExecuteDB: if c.dbs.masterSize == 0 {
D:/work/code/fincore/go/src/utils/gform/session.go:38 (0x177bac4)
	NewSession: s.master = e.GetExecuteDB()
D:/work/code/fincore/go/src/utils/gform/orm.go:22 (0x1770e84)
	NewOrm: orm.SetISession(NewSession(e))
D:/work/code/fincore/go/src/utils/gform/engin.go:243 (0x176f809)
	(*Engin).NewOrm: return NewOrm(c)
D:/work/code/fincore/go/src/model/model.go:58 (0x1924c32)
	DB: orm := engin.NewOrm()
D:/work/code/fincore/go/src/model/business_repayment_bills.go:231 (0x1916386)
	(*BusinessRepaymentBillsService).GetBillByID: data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").Where("id", billID).First()
D:/work/code/fincore/go/src/app/business/repayment/service.go:432 (0x1bc0df0)
	(*PaymentService).createRepaymentInternal: bill, err := s.billModel.GetBillByID(billID)
D:/work/code/fincore/go/src/app/business/repayment/service.go:426 (0x1bc0ca4)
	(*PaymentService).CreateRepayment: return s.createRepaymentInternal(int(123), billID, bankCardID, false)
D:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83 (0x204c927)
	(*PaymentController).CreateRepayment: result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xed60c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xed5072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x18b34c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1955d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1693d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1694145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x16952b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x155d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x155c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x154a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x155a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x1559cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x12728d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x123f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xdc20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:43:54 [Recovery] 2025/08/05 - 09:43:54 panic recovered:
GET /business/repayment/manager/getPaymentStatus?transaction_no=RP1754358122877590963 HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:111 (0xe35616)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/business/repayment/controller.go:100 (0x109740c)
	(*Manager).GetPaymentStatus: claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x3b60c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x3b5072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0xd934c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0xe35d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xb73d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xa3d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xa3c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xb74145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xb752b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xa3d83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xa3c105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xa2a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xa3a1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xa39cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x7528d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x71f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2a20e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:44:53 [Recovery] 2025/08/05 - 09:44:53 panic recovered:
GET /business/repayment/manager/getPaymentStatus?transaction_no=RP1754358122877590963 HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:111 (0xfa5616)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/business/repayment/controller.go:100 (0x120740c)
	(*Manager).GetPaymentStatus: claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x5260c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x525072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0xf034c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0xfa5d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xce3d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xbad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xbac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xce4145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xce52b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xbad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xbac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xbaa1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xba9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x8c28d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x88f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x4120e0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/08/05 09:45:18 [Recovery] 2025/08/05 - 09:45:18 panic recovered:
GET /business/repayment/manager/getPaymentStatus?transaction_no=RP1754358122877590963 HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:111 (0xfa5616)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/business/repayment/controller.go:100 (0x120740c)
	(*Manager).GetPaymentStatus: claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x5260c4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x525072)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0xf034c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0xfa5d64)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xce3d29)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xbad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xbac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xce4145)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xce52b5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xbad83c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xbac105)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xb9a2f9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xbaa1e9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xba9cbb)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x8c28d6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x88f794)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x4120e0)
	goexit: BYTE	$0x90	// NOP
[0m
