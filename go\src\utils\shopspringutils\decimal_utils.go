package shopspringutils

import (
	"fincore/model"

	"github.com/shopspring/decimal"
)

// CeilToTwoDecimal 使用decimal进行精确的向上取整到两位小数
// 只有当小数点第三位不为0时才向上取整，否则直接截断到两位小数
func CeilToTwoDecimal(value float64) float64 {
	decVal := decimal.NewFromFloat(value)
	// 将值乘以1000，检查第三位小数
	thousand := decimal.NewFromInt(1000)
	multipliedBy1000 := decVal.Mul(thousand)
	
	// 获取整数部分，检查第三位小数是否为0
	intPart := multipliedBy1000.IntPart()
	thirdDecimal := intPart % 10
	
	// 将值乘以100处理前两位小数
	hundred := decimal.NewFromInt(100)
	multiplied := decVal.Mul(hundred)
	
	if thirdDecimal != 0 {
		// 第三位不为0，向上取整
		ceiled := multiplied.Ceil()
		return ceiled.Div(hundred).InexactFloat64()
	} else {
		// 第三位为0，直接截断到两位小数
		truncated := multiplied.Truncate(0)
		return truncated.Div(hundred).InexactFloat64()
	}
}

// 使用decimal进行 乘法
func MultiplyAmountsWithDecimal(amount1, amount2 float64) float64 {
	// 使用decimal进行高精度计算
	amount1Decimal := decimal.NewFromFloat(amount1)
	amount2Decimal := decimal.NewFromFloat(amount2)

	// 执行乘法运算
	mulDecimal := amount1Decimal.Mul(amount2Decimal)

	// 转换回float64以保持接口兼容性
	mul, _ := mulDecimal.Float64()
	return mul
}

// AddAmountsWithDecimal 使用decimal库进行高精度加法运算
func AddAmountsWithDecimal(amount1, amount2 float64) float64 {
	// 使用decimal进行高精度计算
	amount1Decimal := decimal.NewFromFloat(amount1)
	amount2Decimal := decimal.NewFromFloat(amount2)

	// 执行加法运算
	sumDecimal := amount1Decimal.Add(amount2Decimal)

	// 转换回float64以保持接口兼容性
	sum, _ := sumDecimal.Float64()
	return sum
}

// DivideAmountsWithDecimal 使用decimal库进行高精度除法运算
func DivideAmountsWithDecimal(amount1, amount2 float64) float64 {
	// 使用decimal进行高精度计算
	amount1Decimal := decimal.NewFromFloat(amount1)
	amount2Decimal := decimal.NewFromFloat(amount2)

	// 执行除法运算
	divDecimal := amount1Decimal.Div(amount2Decimal)

	// 转换回float64以保持接口兼容性
	div, _ := divDecimal.Float64()
	return div
}

// SubtractAmountsWithDecimal 使用decimal库进行高精度减法运算
func SubtractAmountsWithDecimal(amount1, amount2 float64) float64 {
	// 使用decimal进行高精度计算
	amount1Decimal := decimal.NewFromFloat(amount1)
	amount2Decimal := decimal.NewFromFloat(amount2)

	// 执行减法运算
	diffDecimal := amount1Decimal.Sub(amount2Decimal)

	// 转换回float64以保持接口兼容性
	diff, _ := diffDecimal.Float64()
	return diff
}

// CompareAmountsWithDecimal 使用decimal库进行高精度比较
// 返回值：-1 表示 amount1 < amount2，0 表示相等，1 表示 amount1 > amount2
func CompareAmountsWithDecimal(amount1, amount2 float64) int {
	amount1Decimal := decimal.NewFromFloat(amount1)
	amount2Decimal := decimal.NewFromFloat(amount2)
	return amount1Decimal.Cmp(amount2Decimal)
}

// CalculateRemainingFeeWithDecimal 使用decimal库计算剩余费用（考虑减免金额）
// 参数：dueFee-应还费用，submittedAmount-已提交金额，waiveAmount-减免金额
// 返回：剩余费用（确保不为负数）
func CalculateRemainingFeeWithDecimal(dueFee, submittedAmount, waiveAmount float64) float64 {
	// 使用decimal进行高精度计算
	dueFeeDecimal := decimal.NewFromFloat(dueFee)
	submittedAmountDecimal := decimal.NewFromFloat(submittedAmount)
	waiveAmountDecimal := decimal.NewFromFloat(waiveAmount)

	// 计算剩余金额：应还费用 - 已提交金额 - 减免金额
	remainingDecimal := dueFeeDecimal.Sub(submittedAmountDecimal).Sub(waiveAmountDecimal)

	// 确保剩余金额不为负数
	if remainingDecimal.LessThan(decimal.Zero) {
		remainingDecimal = decimal.Zero
	}

	// 转换回float64以保持接口兼容性
	remaining, _ := remainingDecimal.Float64()
	return remaining
}

// CalculateSubmittedAmounts 计算已提交的担保费和资管费金额
// 参数：transactions - 交易流水列表
// 返回：guaranteeAmount - 已提交担保费金额，assetAmount - 已提交资管费金额
func CalculateSubmittedAmounts(transactions []model.BusinessPaymentTransactions) (float64, float64) {
	var guaranteeAmount, assetAmount float64

	// 允许的交易类型
	allowedTypes := map[string]bool{
		model.TransactionTypeRepayment:               true, // REPAYMENT-用户主动还款
		model.TransactionTypeWithhold:                true, // WITHHOLD-系统代扣
		model.TransactionTypePartialOfflineRepayment: true, // PARTIAL_OFFLINE_REPAYMENT-线下部分还款
		model.TransactionTypeManualWithhold:          true, // MANUAL_WITHHOLD-管理员手动代扣
	}

	// 允许的状态
	allowedStatuses := map[int]bool{
		model.TransactionStatusSubmitted: true, // 1-已提交
		model.TransactionStatusSuccess:   true, // 2-处理成功
	}

	// 使用decimal进行高精度计算
	guaranteeAmountDecimal := decimal.Zero
	assetAmountDecimal := decimal.Zero

	for _, transaction := range transactions {
		// 检查交易类型和状态
		if allowedTypes[transaction.Type] && allowedStatuses[transaction.Status] {
			// 根据withhold_type区分担保费和资管费
			if transaction.WithholdType != nil {
				transactionAmount := decimal.NewFromFloat(float64(transaction.Amount))
				switch *transaction.WithholdType {
				case "GUARANTEE": // GUARANTEE-担保
					guaranteeAmountDecimal = guaranteeAmountDecimal.Add(transactionAmount)
				case "ASSET": // ASSET-资管
					assetAmountDecimal = assetAmountDecimal.Add(transactionAmount)
				}
			}
		} else if transaction.Type == model.TransactionTypeRefund && allowedStatuses[transaction.Status] {
			// 减去退款金额
			if transaction.WithholdType != nil {
				transactionAmount := decimal.NewFromFloat(float64(transaction.Amount))
				switch *transaction.WithholdType {
				case "GUARANTEE":
					guaranteeAmountDecimal = guaranteeAmountDecimal.Sub(transactionAmount)
				case "ASSET":
					assetAmountDecimal = assetAmountDecimal.Sub(transactionAmount)
				}
			}
		}
	}

	// 转换回float64以保持接口兼容性
	guaranteeAmount, _ = guaranteeAmountDecimal.Float64()
	assetAmount, _ = assetAmountDecimal.Float64()

	return guaranteeAmount, assetAmount
}

// CalculateRemainingAmounts 计算减免后的实际应还金额
// 参数：totalDueAmount-账单总应还金额，totalWaiveAmount-总减免金额，dueGuaranteeFee-应还担保费，dueAssetFee-应还资管费
// 返回：remainingGuaranteeAmount-实际应还担保费，remainingAssetAmount-实际应还资管费
// 逻辑：总额先减去减免额得到应还总额，再按原先担保占总额的比例计算新的担保和资管费用分配
func CalculateRemainingAmounts(totalDueAmount, totalWaiveAmount, dueGuaranteeFee, dueAssetFee float64) (remainingGuaranteeAmount, remainingAssetAmount float64) {
	// 使用decimal进行高精度计算
	totalDueAmountDecimal := decimal.NewFromFloat(totalDueAmount)
	totalWaiveAmountDecimal := decimal.NewFromFloat(totalWaiveAmount)
	dueGuaranteeFeeDecimal := decimal.NewFromFloat(dueGuaranteeFee)
	dueAssetFeeDecimal := decimal.NewFromFloat(dueAssetFee)

	// 如果总应还金额为0，直接返回0
	if totalDueAmountDecimal.LessThanOrEqual(decimal.Zero) {
		return 0, 0
	}

	// 如果总减免金额为0，直接返回原金额
	if totalWaiveAmountDecimal.LessThanOrEqual(decimal.Zero) {
		remainingGuaranteeAmount, _ = dueGuaranteeFeeDecimal.Float64()
		remainingAssetAmount, _ = dueAssetFeeDecimal.Float64()
		return remainingGuaranteeAmount, remainingAssetAmount
	}

	// 计算减免后的应还总额
	remainingTotalDecimal := totalDueAmountDecimal.Sub(totalWaiveAmountDecimal)

	// 如果应还总额小于等于0，说明减免金额已覆盖全部费用
	if remainingTotalDecimal.LessThanOrEqual(decimal.Zero) {
		return 0, 0
	}

	// 计算担保费占总应还金额的比例
	guaranteeRatioDecimal := dueGuaranteeFeeDecimal.Div(totalDueAmountDecimal)

	// 按原先比例计算新的担保应还金额
	newGuaranteeAmountDecimal := remainingTotalDecimal.Mul(guaranteeRatioDecimal)

	newGuaranteeAmountDecimal = ceilToTwoDecimalShop(newGuaranteeAmountDecimal)

	// 新的资管应还金额 = 应还总额 - 新的担保应还金额
	newAssetAmountDecimal := remainingTotalDecimal.Sub(newGuaranteeAmountDecimal)

	// 确保金额不为负数
	if newGuaranteeAmountDecimal.LessThan(decimal.Zero) {
		newGuaranteeAmountDecimal = decimal.Zero
	}
	if newAssetAmountDecimal.LessThan(decimal.Zero) {
		newAssetAmountDecimal = decimal.Zero
	}

	// 转换回float64
	remainingGuaranteeAmount, _ = newGuaranteeAmountDecimal.Float64()
	remainingAssetAmount, _ = newAssetAmountDecimal.Float64()
	return remainingGuaranteeAmount, remainingAssetAmount
}

// CeilToTwoDecimal 使用decimal进行精确的向上取整到两位小数
// 只有当小数点第三位不为0时才向上取整，否则直接截断到两位小数
func ceilToTwoDecimalShop(value decimal.Decimal) decimal.Decimal {
	// 将值乘以1000，检查第三位小数
	thousand := decimal.NewFromInt(1000)
	multipliedBy1000 := value.Mul(thousand)

	// 获取整数部分，检查第三位小数是否为0
	intPart := multipliedBy1000.IntPart()
	thirdDecimal := intPart % 10

	// 将值乘以100处理前两位小数
	hundred := decimal.NewFromInt(100)
	multiplied := value.Mul(hundred)

	if thirdDecimal != 0 {
		// 第三位不为0，向上取整
		ceiled := multiplied.Ceil()
		return ceiled.Div(hundred)
	} else {
		// 第三位为0，直接截断到两位小数
		truncated := multiplied.Truncate(0)
		return truncated.Div(hundred)
	}
}
