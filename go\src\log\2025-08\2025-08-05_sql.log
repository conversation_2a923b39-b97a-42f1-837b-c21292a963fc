{"level":"dev.info","ts":"[2025-08-05 09:33:27.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"48.8534ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.190]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"30.631ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.224]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_payment_transactions` WHERE `bill_id` = ? and (deleted_at IS NULL) ORDER BY created_at DESC, [649]","duration":"34.7078ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.245]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"17.1325ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.261]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"16.7628ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.282]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [158]","duration":"20.3752ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.304]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"INSERT INTO `business_payment_transactions` (`payment_channel_id`,`third_party_order_no`,`status`,`channel_transaction_no`,`order_no`,`related_transaction_no`,`completed_at`,`user_id`,`withhold_type`,`offline_payment_voucher`,`order_id`,`error_code`,`remark`,`type`,`error_message`,`amount`,`offline_payment_channel_detail`,`callback_result`,`created_at`,`transaction_no`,`bill_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [1 RP1754357607283207181 0  LO20250805ENQGAGQI <nil> <nil> 123 0xc000d48110 <nil> 158  <nil> REPAYMENT  366.22 <nil> <nil> 2025-08-05 09:33:27.2838535 +0800 CST m=+3.586443501 RP1754357607283207181 0xc000a4e6e8]","duration":"20.4497ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.657]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`error_message` = ? WHERE `transaction_no` = ?, [3 支付失败: 签名验证失败[EG000001] RP1754357607283207181]","duration":"24.0936ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.667]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"9.7553ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.678]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"11.0788ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.689]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [158]","duration":"11.2ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858baf27cc623a848e96582","sql":"INSERT INTO `business_payment_transactions` (`payment_channel_id`,`remark`,`related_transaction_no`,`status`,`bill_id`,`callback_result`,`type`,`offline_payment_channel_detail`,`order_no`,`transaction_no`,`third_party_order_no`,`amount`,`completed_at`,`offline_payment_voucher`,`channel_transaction_no`,`withhold_type`,`user_id`,`created_at`,`error_message`,`error_code`,`order_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [1 <nil> <nil> 0 0xc000798ff0 <nil> REPAYMENT <nil> LO20250805ENQGAGQI RP1754357619689156077 RP1754357619689156077 5 <nil> <nil>  0xc000dfc410 123 2025-08-05 09:33:39.6899108 +0800 CST m=+15.*********   158]","duration":"13.5733ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.842]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"11.4559ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.856]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"11.8279ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_payment_transactions` WHERE `bill_id` = ? and (deleted_at IS NULL) ORDER BY created_at DESC, [649]","duration":"13.965ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.882]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"11.633ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.892]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"9.8482ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.902]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [158]","duration":"9.6747ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.925]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"INSERT INTO `business_payment_transactions` (`bill_id`,`transaction_no`,`payment_channel_id`,`created_at`,`channel_transaction_no`,`withhold_type`,`amount`,`callback_result`,`type`,`completed_at`,`third_party_order_no`,`error_code`,`remark`,`order_no`,`offline_payment_channel_detail`,`offline_payment_voucher`,`related_transaction_no`,`user_id`,`status`,`order_id`,`error_message`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [0xc0009da508 RP1754357795902500624 1 2025-08-05 09:36:35.9025107 +0800 CST m=+5.285060701  0xc000b08dc0 366.22 <nil> REPAYMENT <nil> RP1754357795902500624  <nil> LO20250805ENQGAGQI <nil> <nil> <nil> 123 0 158 ]","duration":"22.0646ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.425]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"UPDATE `business_payment_transactions` SET `third_party_order_no` = ?,`completed_at` = ?,`status` = ?,`error_message` = ?,`channel_transaction_no` = ? WHERE `transaction_no` = ?, [RP1754357795902500624 2025-08-05 09:36:53.4136363 +0800 CST m=+22.796186301 1  T20905711 RP1754357795902500624]","duration":"11.8241ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb1e65d8534c70b23a2d","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754357795902500624]","duration":"23.694ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.457]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb27cad47d58beffa7a1","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [649]","duration":"15.0586ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.470]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb27cad47d58beffa7a1","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? ORDER BY id LIMIT 1, [74]","duration":"12.4114ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.492]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1858bb27cad47d58beffa7a1","sql":"SELECT * FROM `business_payment_transactions` WHERE `bill_id` = ? and (deleted_at IS NULL) ORDER BY created_at DESC, [649]","duration":"21.8465ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"15.1405ms","duration_ms":15}
{"level":"dev.error","ts":"[2025-08-05 09:44:00.035]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"45.5883ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"50.2988ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"28.2669ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"29.8071ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"72.2606ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-08-05 09:45:59.881]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"26.0828ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-05 09:47:19.049]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"39.1196ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.215]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"18.5075ms","duration_ms":18}
{"level":"dev.error","ts":"[2025-08-05 09:48:05.244]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"62.5937ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"68.0443ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.293]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.0509ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"9.9029ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.049]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"9.8847ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"36.0594ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.251]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"16.8945ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.265]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"14.349ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.284]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"18.7229ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.456]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"30.9157ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.620]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `completed_at` = ?,`status` = ? WHERE `transaction_no` = ?, [2025-08-05 09:48:11.6094846 +0800 CST m=+55.475720201 1 RP1754358203898784233]","duration":"11.002ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.639]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"19.258ms","duration_ms":19}
{"level":"dev.error","ts":"[2025-08-05 09:48:35.249]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"18.6551ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"19.159ms","duration_ms":19}
{"level":"dev.error","ts":"[2025-08-05 09:49:05.275]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:49:35.282]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.011]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"10.99ms","duration_ms":10}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.042]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"47.1101ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.049]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"48.6688ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.068]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"19.4196ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.276]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"19.0952ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"12.9786ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.316]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"26.0795ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.501]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"29.7221ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.512]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"10.7873ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.528]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"16.1972ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.685]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"28.6873ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.851]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`completed_at` = ? WHERE `transaction_no` = ?, [1 2025-08-05 09:50:00.8407541 +0800 CST m=+164.706989701 RP1754358203898784233]","duration":"10.4098ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"16.5883ms","duration_ms":16}
{"level":"dev.error","ts":"[2025-08-05 09:50:30.047]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"43.4197ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"46.1339ms","duration_ms":46}
{"level":"dev.error","ts":"[2025-08-05 09:51:00.056]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:51:30.076]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.033]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"37.6399ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"37.6399ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"38.1432ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"13.2214ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"13.7045ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.252]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"9.7142ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.274]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"21.8787ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.435]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"12.5951ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"13.6125ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.462]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"12.8703ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"19.7713ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.781]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `completed_at` = ?,`status` = ? WHERE `transaction_no` = ?, [2025-08-05 09:52:00.7698894 +0800 CST m=+284.636125001 1 RP1754358203898784233]","duration":"11.5872ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"35.9528ms","duration_ms":35}
{"level":"dev.error","ts":"[2025-08-05 09:52:30.045]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"51.6527ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"60.0561ms","duration_ms":60}
{"level":"dev.error","ts":"[2025-08-05 09:53:00.075]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:53:30.082]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.031]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"41.5044ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"57.335ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"57.335ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"32.548ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.305]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"13.0599ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"12.5784ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"32.2257ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.508]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"9.2643ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"11.5723ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"10.889ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.716]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"22.3581ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.880]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`completed_at` = ? WHERE `transaction_no` = ?, [1 2025-08-05 09:54:00.866904 +0800 CST m=+404.733139601 RP1754358203898784233]","duration":"13.3973ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"23.2027ms","duration_ms":23}
{"level":"dev.error","ts":"[2025-08-05 09:54:30.067]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"45.9817ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"45.9817ms","duration_ms":45}
{"level":"dev.error","ts":"[2025-08-05 09:55:00.076]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:55:30.089]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"84.1857ms","duration_ms":84}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.084]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"88.2974ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"88.8045ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"19.0167ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"16.3566ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.323]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"14.2488ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"12.466ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.488]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"9.36ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.502]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"12.951ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.528]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"25.715ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.682]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"24.1571ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.846]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `completed_at` = ?,`status` = ? WHERE `transaction_no` = ?, [2025-08-05 09:56:00.8257293 +0800 CST m=+524.691964901 1 RP1754358203898784233]","duration":"20.5694ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"11.1588ms","duration_ms":11}
{"level":"dev.error","ts":"[2025-08-05 09:56:30.106]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"42.8852ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"42.372ms","duration_ms":42}
{"level":"dev.error","ts":"[2025-08-05 09:57:00.138]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:57:30.145]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.045]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"44.7316ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"53.829ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"53.829ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"31.7033ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.282]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"34.4001ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.302]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"19.2984ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"47.6528ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.533]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"26.0157ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.547]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"13.4356ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.579]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"32.5336ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.747]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"31.9375ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.908]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`completed_at` = ? WHERE `transaction_no` = ?, [1 2025-08-05 09:58:00.8991964 +0800 CST m=+644.765432001 RP1754358203898784233]","duration":"9.2755ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.932]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754358203898784233]","duration":"24.0523ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 09:58:20.610]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.4438ms","duration_ms":29}
{"level":"dev.error","ts":"[2025-08-05 09:58:41.635]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"63.0401ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"63.5797ms","duration_ms":63}
{"level":"dev.error","ts":"[2025-08-05 09:59:11.640]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:59:36.459]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.4576ms","duration_ms":29}
{"level":"dev.error","ts":"[2025-08-05 11:18:00.009]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"62.2402ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"62.2402ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"62.2402ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.6807ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.070]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"13.0604ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.083]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"12.4843ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"25.8589ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.268]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"11.7574ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.280]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"12.436ms","duration_ms":12}
{"level":"dev.error","ts":"[2025-08-05 11:18:30.019]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"50.0956ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"50.0956ms","duration_ms":50}
{"level":"dev.error","ts":"[2025-08-05 11:19:00.051]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:19:30.062]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.059]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"task_repayment-status-sync-compensation_20250805112000","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805112000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"69.0561ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805112000","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"69.3524ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805112000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"70.1182ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805112000","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"20.5239ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.269]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"17.8649ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"9.5667ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"45.5753ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"68.4397ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.587]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"42.9968ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"31.1882ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"67.9055ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"49.9031ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"49.9031ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"20.1507ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"20.1507ms","duration_ms":20}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.058]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"73.6122ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.113]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"38.4091ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.297]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"12.3624ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.311]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"13.2256ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"25.239ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.499]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"12.4553ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.510]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"11.1303ms","duration_ms":11}
{"level":"dev.error","ts":"[2025-08-05 11:24:30.066]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"41.4326ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"41.4326ms","duration_ms":41}
{"level":"dev.error","ts":"[2025-08-05 11:25:00.071]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:25:30.077]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.051]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"62.2755ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"62.2755ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.070]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"69.8223ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"11.0165ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"11.5201ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.083]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `contracts` WHERE `id` = ? LIMIT 1, [143]","duration":"9.2441ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [139]","duration":"13.1245ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [139]","duration":"13.1245ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `id` = ? LIMIT 1, [75]","duration":"18.9999ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [139]","duration":"22.7756ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [166 DISBURSEMENT]","duration":"15.8474ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.254]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"14.2072ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.267]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"12.9851ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"21.9324ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.513]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"26.0831ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.524]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"9.9921ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"10.8502ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"41.9232ms","duration_ms":41}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.041]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"59.6841ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"65.3254ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"65.3254ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"15.0235ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.291]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"15.4503ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"20.8121ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.329]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"16.1874ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.525]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"32.0803ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.536]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"10.9158ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754364402669755512]","duration":"36.4265ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.761]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`completed_at` = ? WHERE `transaction_no` = ?, [1 2025-08-05 11:28:00.7441169 +0800 CST m=+86.346480801 RP1754364402669755512]","duration":"17.7995ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.789]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754364402669755512]","duration":"27.6902ms","duration_ms":27}
{"level":"dev.error","ts":"[2025-08-05 11:28:30.046]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"46.3942ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"45.8054ms","duration_ms":45}
{"level":"dev.error","ts":"[2025-08-05 11:29:00.074]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.015]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"14.5188ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"34.0842ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805113800","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"259.0423ms","duration_ms":259}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805113800","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"259.0423ms","duration_ms":259}
{"level":"dev.error","ts":"[2025-08-05 11:38:00.259]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"task_repayment-status-sync-compensation_20250805113800","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.260]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805113800","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"256.6797ms","duration_ms":256}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.305]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250805113800","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"45.1754ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"38.2964ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.611]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"57.4284ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.698]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"86.4993ms","duration_ms":86}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"147.8686ms","duration_ms":147}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"57.5432ms","duration_ms":57}
{"level":"dev.error","ts":"[2025-08-05 11:38:30.266]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"750.5299ms","duration_ms":750}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"750.5299ms","duration_ms":750}
{"level":"dev.error","ts":"[2025-08-05 11:39:00.751]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.020]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.101]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"97.1253ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"97.8188ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"101.4041ms","duration_ms":101}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.135]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"30.1352ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.356]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"21.6858ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.386]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"29.9118ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.407]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"20.5121ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.592]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"23.5184ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.621]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"27.6903ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"59.1259ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"129.0052ms","duration_ms":129}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.053]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"55.7266ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"65.5327ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.070]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"68.5435ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"31.384ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"24.4037ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.345]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"14.3844ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.393]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"48.7201ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.566]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"27.8793ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.587]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"21.072ms","duration_ms":21}
{"level":"dev.error","ts":"[2025-08-05 11:42:30.061]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"47.9096ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.086]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"84.0784ms","duration_ms":84}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.050]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"61.2963ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"66.6363ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"66.6363ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"23.9837ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.300]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"27.1341ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.323]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"21.4206ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"24.3974ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.524]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"31.644ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.549]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"25.6253ms","duration_ms":25}
{"level":"dev.error","ts":"[2025-08-05 11:48:00.014]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.1791ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"15.8942ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"16.4208ms","duration_ms":16}
{"level":"dev.error","ts":"[2025-08-05 11:48:30.017]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.009]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"8.5445ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.010]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"9.6163ms","duration_ms":9}
{"level":"dev.error","ts":"[2025-08-05 11:49:00.020]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:49:30.023]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:50:00.009]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.009]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"9.2222ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.010]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"9.7639ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.011]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"10.3925ms","duration_ms":10}
{"level":"dev.error","ts":"[2025-08-05 11:50:30.012]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.9955ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"19.042ms","duration_ms":19}
{"level":"dev.error","ts":"[2025-08-05 11:51:00.025]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"93.877ms","duration_ms":93}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"155.6475ms","duration_ms":155}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.158]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.167]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"166.49ms","duration_ms":166}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.192]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"24.2298ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.469]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"41.4756ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.502]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"32.4861ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"28.5762ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"11.3721ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.804]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"15.6577ms","duration_ms":15}
{"level":"dev.error","ts":"[2025-08-05 11:52:30.171]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"123.4197ms","duration_ms":123}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"132.4797ms","duration_ms":132}
{"level":"dev.error","ts":"[2025-08-05 11:53:00.178]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:53:30.186]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.064]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"71.6828ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"78.9519ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"79.4646ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.114]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"34.8235ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.317]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"14.186ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"18.6951ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.371]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"34.8256ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.559]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"18.0156ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.584]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"24.4806ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"17.5912ms","duration_ms":17}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.044]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"54.7773ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"55.2944ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.068]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"12.1371ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.295]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"24.1105ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.307]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"11.5335ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.3669ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.547]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"35.5134ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.563]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"16.3667ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.011]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"10.3321ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"64.5726ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"49.6717ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"49.1674ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"49.1674ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.058]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"57.7041ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"16.0526ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.269]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"10.1316ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.283]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"13.9062ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"29.4381ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.475]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"11.3161ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.488]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"13.2539ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"54.2159ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"54.2159ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"38.3113ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"26.3122ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"84.3404ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.086]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"84.8505ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"89.9266ms","duration_ms":89}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.298]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [583]","duration":"28.7922ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.312]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [148]","duration":"14.0858ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.346]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"33.9275ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.504]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_repayment_bills` WHERE `id` = ? LIMIT 1, [593]","duration":"13.1612ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.524]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [149]","duration":"20.7522ms","duration_ms":20}
{"level":"dev.error","ts":"[2025-08-05 14:40:30.009]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1054 (42S22): Unknown column 'ba.availableProductID' in 'field list'","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:185\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"25.3552ms","duration_ms":25}
{"level":"dev.error","ts":"[2025-08-05 14:41:00.040]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1054 (42S22): Unknown column 'ba.availableProductID' in 'field list'","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:185\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 14:41:50.018]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'FROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tW' at line 10","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:184\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:42:20.014]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"13.2071ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-05 14:42:30.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"25.6449ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-05 14:42:40.014]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"12.8242ms","duration_ms":12}
