{"level":"dev.info","ts":"[2025-08-05 09:26:00.001]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.047]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.047]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:46:04.761]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.196]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.264]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.264]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.049]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.049]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.266]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.266]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.425]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.425]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.640]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.640]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"6.4436552s"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.049]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.049]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.290]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.290]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.512]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.512]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.656]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.656]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"867.1589ms"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.252]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.252]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.449]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.449]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.581]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.582]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"818.4559ms"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.318]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.318]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.520]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.520]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.693]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.693]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"903.4796ms"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.089]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.089]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.323]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.323]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.502]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.502]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.657]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.657]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"857.0855ms"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":4}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.302]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.302]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.547]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.547]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"transaction_type":"MANUAL_WITHHOLD","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.715]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754358122877590963","bill_id":653,"operation":"sync_status_failed","error":"查询支付状态失败: 第三方查询失败: tant004 - 签名验证失败[EG000001]","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.715]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.932]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754358203898784233","bill_id":654,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.932]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":4,"success_count":1,"failure_count":3,"duration":"932.1421ms"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.001]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.064]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.064]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.083]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.083]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.280]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.280]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"11.279093s"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.002]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.072]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.072]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.278]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.278]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.587]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.587]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"584.9722ms"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.074]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.074]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.311]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.311]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.510]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.510]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"510.3276ms"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.062]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.062]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.267]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.267]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.524]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.524]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"523.7158ms"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.060]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.060]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.313]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.313]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.536]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.536]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754364402669755512","bill_id":696,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.790]","caller":"repayment/repayment_status_sync_compensation.go:137","msg":"支付流水状态同步成功","task":"repayment-status-sync-compensation","transaction_no":"RP1754364402669755512","bill_id":696,"operation":"sync_status_success"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.790]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":3,"success_count":1,"failure_count":2,"duration":"789.3022ms"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.002]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.260]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.260]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:38:00.611]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.611]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:38:01.322]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.322]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"1.3194257s"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.001]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.105]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.105]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.386]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.386]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.621]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.621]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"618.9745ms"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.001]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.070]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.070]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.345]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.345]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.587]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.587]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"584.9996ms"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.062]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.062]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.323]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.323]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.549]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.549]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"548.7245ms"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"repayment/repayment_status_sync_compensation.go:79","msg":"未找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"no_transactions_found"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.010]","caller":"repayment/repayment_status_sync_compensation.go:79","msg":"未找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"no_transactions_found"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.167]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.167]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.502]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.502]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.804]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.804]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"803.0863ms"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.336]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.336]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.584]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.584]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"583.8564ms"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.307]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.308]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.563]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.563]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"562.4903ms"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.283]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.284]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.488]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.488]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"487.567ms"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.000]","caller":"repayment/repayment_status_sync_compensation.go:59","msg":"开始执行还款状态同步补偿任务","task":"repayment-status-sync-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.040]","caller":"repayment/repayment_status_sync_compensation.go:87","msg":"找到需要同步状态的支付流水","task":"repayment-status-sync-compensation","operation":"found_transactions","transaction_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.040]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.312]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754297885279933166","bill_id":583,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.312]","caller":"repayment/repayment_status_sync_compensation.go:115","msg":"开始同步支付流水状态","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"transaction_type":"REPAYMENT","operation":"sync_status"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.524]","caller":"repayment/repayment_status_sync_compensation.go:127","msg":"支付流水状态同步失败","task":"repayment-status-sync-compensation","transaction_no":"RP1754298853035585483","bill_id":593,"operation":"sync_status_failed","error":"查询支付状态失败: 更新账单和订单已还金额失败: 查询订单失败: 订单不存在","stacktrace":"fincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:127\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.524]","caller":"repayment/repayment_status_sync_compensation.go:149","msg":"还款状态同步补偿任务执行完成","task":"repayment-status-sync-compensation","operation":"execution_completed","processed_count":2,"success_count":0,"failure_count":2,"duration":"524.1335ms"}
