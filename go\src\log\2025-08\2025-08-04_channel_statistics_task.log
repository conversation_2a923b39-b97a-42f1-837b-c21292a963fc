{"level":"dev.info","ts":"[2025-08-04 16:22:50.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804162250"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/channel_statistics_task.go:60","msg":"渠道统计任务执行失败","request_id":"task_channel_statistics_task_20250804162250","error":"部分渠道统计失败，错误数量: 9","stacktrace":"fincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:60\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.105]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804162250","task_name":"channel_statistics_task","complete_time":"2025-08-04 16:22:50"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.105]","caller":"statistics/channel_statistics_task.go:101","msg":"渠道统计任务执行失败","request_id":"task_channel_statistics_task_20250804162250","error":"部分渠道统计失败，错误数量: 9","task_name":"channel_statistics_task","error_time":"2025-08-04 16:22:50","stacktrace":"fincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).OnError\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:101\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:238\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804162630"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.001]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804162810"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.035]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804162950"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804163940"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804163940","request_id":"task_channel_statistics_task_20250804163950"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804164230"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804164310"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804164500"}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804164620"}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.001]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804165530"}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804165710"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.001]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170450"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170450"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170450","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:04:50"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170450","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:04:50"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170450","request_id":"task_channel_statistics_task_20250804170500"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170450","request_id":"task_channel_statistics_task_20250804170500"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170450","request_id":"task_channel_statistics_task_20250804170500","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:05:00"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170450","request_id":"task_channel_statistics_task_20250804170500","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:05:00"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170620"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170620"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170620","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:06:20"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170620","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:06:20"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:06:30"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:06:30"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","request_id":"task_channel_statistics_task_20250804170640"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","request_id":"task_channel_statistics_task_20250804170640"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","request_id":"task_channel_statistics_task_20250804170640","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:06:40"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170620","request_id":"task_channel_statistics_task_20250804170630","request_id":"task_channel_statistics_task_20250804170640","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:06:40"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170940"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170940"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170940","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:09:40"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170940","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:09:40"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:09:50"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:09:50"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"statistics/channel_statistics_task.go:52","msg":"开始执行渠道统计定时任务","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","request_id":"task_channel_statistics_task_20250804171000"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/channel_statistics_task.go:74","msg":"渠道统计定时任务执行完成","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","request_id":"task_channel_statistics_task_20250804171000"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/channel_statistics_task.go:114","msg":"渠道统计任务执行完成","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","request_id":"task_channel_statistics_task_20250804171000","task_name":"channel_statistics_task","complete_time":"2025-08-04 17:10:00"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/channel_statistics_task.go:89","msg":"渠道统计任务执行成功","request_id":"task_channel_statistics_task_20250804170940","request_id":"task_channel_statistics_task_20250804170950","request_id":"task_channel_statistics_task_20250804171000","task_name":"channel_statistics_task","execution_time":"2025-08-04 17:10:00"}
