package risk

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"fincore/global"
	"fincore/model"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

// restoreWorkingDirectory 恢复原始工作目录
func restoreWorkingDirectory(originalPath string) {
	if originalPath != "" {
		os.Chdir(originalPath)
	}
}

func init() {
	// 设置测试环境
	_, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

}

// setupRiskService 设置风控服务
func setupRiskService() *RiskService {
	return NewRiskService(context.Background())
}

// TestRiskSystemIntegration 测试风控系统的主要功能
func TestRiskSystemIntegration(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()
	testCustomerID := uint64(134)

	// 测试授信评估
	t.Run("授信评估测试", func(t *testing.T) {
		evalResp, err := riskService.EvaluateRisk(ctx, model.DB(), int64(testCustomerID))
		if err != nil {
			t.Logf("授信评估失败: %v", err)
			return
		}

		assert.NotNil(t, evalResp)
		assert.NotEmpty(t, evalResp.RiskReportID)
		assert.GreaterOrEqual(t, evalResp.RiskScore, 0)
		assert.LessOrEqual(t, evalResp.RiskScore, 1000)
		assert.NotEmpty(t, evalResp.EvaluationTime)
		t.Logf("授信评估成功: ID=%s, Score=%d, Result=%d", evalResp.RiskReportID, evalResp.RiskScore, evalResp.RiskResult)
	})

	// 等待数据保存
	time.Sleep(100 * time.Millisecond)

	// 测试贷款产品匹配
	t.Run("贷款产品匹配测试", func(t *testing.T) {
		productResp, err := riskService.GetLoanProducts(ctx, int64(testCustomerID))
		if err != nil {
			t.Logf("获取贷款产品失败: %v", err)
			return
		}

		assert.NotNil(t, productResp)
		assert.GreaterOrEqual(t, productResp.OverallCreditLimit, 0.0)
		t.Logf("产品匹配成功: 总额度=%.2f, 产品数量=%d", productResp.OverallCreditLimit, len(productResp.Products))
	})

	//带产品的评估
	t.Run("带产品的风险评估测试", func(t *testing.T) {
		// 准备测试数据
		productEvalParams := EvaluateRiskWithProductParams{
			CustomerID: uint64(testCustomerID),
			ChannelID:  uint64(1), // 使用存在的渠道ID
			ProductID:  uint64(9), // 使用存在的产品ID
		}

		// 调用带产品的风险评估
		productEvalResp, err := riskService.EvaluateRiskWithProduct(ctx, productEvalParams)
		if err != nil {
			t.Logf("带产品的风险评估失败: %v", err)
			return
		}

		assert.NotNil(t, productEvalResp)
		assert.NotEmpty(t, productEvalResp.RiskReportID)
		assert.GreaterOrEqual(t, productEvalResp.RiskScore, 0)
		assert.GreaterOrEqual(t, productEvalResp.AvailableCreditLimit, 0.0)
		t.Logf("带产品的风险评估成功: ID=%s, Score=%d, Result=%d, 可用额度=%.2f",
			productEvalResp.RiskReportID, productEvalResp.RiskScore, productEvalResp.RiskResult, productEvalResp.AvailableCreditLimit)
	})

	// 测试风控报告查询
	t.Run("风控报告查询测试", func(t *testing.T) {
		reportData := map[string]interface{}{
			"customer_id": testCustomerID,
		}
		reports, err := riskService.GetRiskReports(ctx, reportData)
		if err != nil {
			t.Logf("获取风控报告失败: %v", err)
			return
		}

		if len(reports) == 0 {
			t.Log("没有找到风控报告记录")
			return
		}
		// 打印所有报告
		for _, report := range reports {
			t.Logf("报告查询成功: ID=%s, Score=%d, Result=%d", report.EvaluationID, report.RiskScore, report.RiskResult)
			t.Logf("报告查询成功: RawData=%s", report.RawData)
		}

	})

	//PerformNewEvaluation
	t.Run("PerformNewEvaluation测试", func(t *testing.T) {
		customerInfo, err := riskService.GetCustomerInfo(ctx, int64(testCustomerID))
		if err != nil {
			t.Logf("获取客户信息失败: %v", err)
			return
		}
		evaluation, err := riskService.PerformNewEvaluation(ctx, model.DB(), customerInfo)
		if err != nil {
			t.Logf("评估失败: %v", err)
			return
		}
		assert.NotNil(t, evaluation)
		// 检查评估结果
		assert.GreaterOrEqual(t, evaluation.RiskScore, 0)
		assert.LessOrEqual(t, evaluation.RiskScore, 1000)
		assert.GreaterOrEqual(t, evaluation.RiskResult, 0)
		assert.LessOrEqual(t, evaluation.RiskResult, 3)
	})

}

// TestRiskEvaluationOnly 单独测试授信评估功能
func TestRiskEvaluationOnly(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()
	testCases := []struct {
		name       string
		customerID uint64
		expectErr  bool
	}{
		{"正常客户", 17, false},
		{"高风险客户", 18, false},
		{"空客户ID", 0, true},
		{"字符串客户ID", 0, true}, // 测试参数转换
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 对于无效的客户ID测试，直接传递0或负数
			var customerID int64
			if tc.name == "字符串客户ID" || tc.customerID == 0 {
				customerID = 0 // 无效的客户ID
			} else {
				customerID = int64(tc.customerID)
			}

			resp, err := riskService.EvaluateRisk(ctx, model.DB(), customerID)
			if tc.expectErr {
				assert.Error(t, err)
				t.Logf("预期错误: %v", err)
				return
			}

			if err != nil {
				t.Logf("%s 失败(可能是测试环境问题): %v", tc.name, err)
				return
			}
			assert.NotNil(t, resp)
			if resp != nil {
				assert.NotEmpty(t, resp.RiskReportID)
				assert.GreaterOrEqual(t, resp.RiskScore, 0)
				assert.LessOrEqual(t, resp.RiskScore, 1000)
				assert.GreaterOrEqual(t, resp.RiskResult, 0)
				assert.LessOrEqual(t, resp.RiskResult, 3)
				assert.NotEmpty(t, resp.EvaluationTime)
				t.Logf("%s 成功: ID=%s, Score=%d, Result=%d", tc.name, resp.RiskReportID, resp.RiskScore, resp.RiskResult)
			}
		})
	}
}

// TestGetProductsByAmount 测试根据额度获取产品功能
func TestGetProductsByAmount(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()

	// 使用指定额度进行测试
	loanAmount := 0.0
	products, err := riskService.GetProductsByAmount(ctx, loanAmount)

	assert.NoError(t, err)
	if products == nil {
		products = []*model.ProductRules{}
	}
	t.Logf("查询额度 %.2f: 查询到 %d 个产品", loanAmount, len(products))

	// 验证返回的产品额度都小于等于请求额度
	for _, product := range products {
		assert.NotNil(t, product)
		assert.LessOrEqual(t, product.LoanAmount, loanAmount)
		t.Logf("产品: ID=%d, 名称=%s, 额度=%.2f", product.ID, product.RuleName, product.LoanAmount)
	}
}

// TestRiskReport 测试风控报告查询功能
func TestRiskReport(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()

	// 创建测试评估记录
	_, err := riskService.EvaluateRisk(ctx, model.DB(), int64(17))
	if err != nil {
		t.Logf("创建测试记录失败: %v", err)
	}
	time.Sleep(100 * time.Millisecond)

	testCases := []struct {
		name       string
		customerID int64
		expectErr  bool
	}{
		{"正常查询", 17, false},
		{"不存在客户", 9999, false},
		{"空客户ID", 0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reqData := map[string]interface{}{"customer_id": tc.customerID}
			reports, err := riskService.GetRiskReports(ctx, reqData)

			if tc.expectErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, reports)
			t.Logf("%s: 查询到 %d 条记录", tc.name, len(reports))

			if len(reports) > 0 {
				resp := reports[0]
				assert.NotEmpty(t, resp.EvaluationID)
				assert.GreaterOrEqual(t, resp.RiskScore, 0)
				assert.LessOrEqual(t, resp.RiskScore, 1000)
				assert.NotEmpty(t, resp.EvaluationTime)
			}
		})
	}
}

// TestRiskServiceIntegration 集成测试
func TestRiskServiceIntegration(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()
	testCustomerID := int64(93)

	t.Run("完整流程测试", func(t *testing.T) {
		// 1. 执行风控评估
		t.Log("步骤1: 执行风控评估")
		evalResp, err := riskService.EvaluateRisk(ctx, model.DB(), testCustomerID)
		if err != nil {
			t.Fatalf("风控评估失败: %v", err)
		}
		t.Logf("评估完成: %s", evalResp.RiskReportID)

		// 2. 获取贷款产品
		t.Log("步骤2: 获取贷款产品")
		productResp, err := riskService.GetLoanProducts(ctx, testCustomerID)
		if err != nil {
			t.Fatalf("获取产品失败: %v", err)
		}
		t.Logf("产品匹配完成: %d个产品", len(productResp.Products))

		// 3. 查询风控报告
		t.Log("步骤3: 查询风控报告")
		testCustomerID := int64(93)
		reports, err := riskService.GetRiskReports(ctx, map[string]interface{}{"customer_id": testCustomerID})
		if err != nil {
			t.Fatalf("查询报告失败: %v", err)
		}
		if len(reports) == 0 {
			t.Fatal("应该至少有一条风控报告记录")
		}
		reportResp := reports[0]
		t.Logf("报告查询完成: %s", reportResp.EvaluationID)

		// 4. 验证数据一致性
		t.Log("步骤4: 验证数据一致性")
		if evalResp.RiskReportID != reportResp.EvaluationID {
			t.Errorf("评估ID不一致: %s != %s", evalResp.RiskReportID, reportResp.EvaluationID)
		}
		if evalResp.RiskScore != reportResp.RiskScore {
			t.Errorf("风险分数不一致: %d != %d", evalResp.RiskScore, reportResp.RiskScore)
		}

		// 5. 使用匹配到的产品测试 EvaluateRiskWithProduct
		t.Log("步骤5: 测试带产品的风控评估")
		if len(productResp.Products) > 0 {
			firstProduct := productResp.Products[0]
			productParams := EvaluateRiskWithProductParams{
				CustomerID: uint64(testCustomerID),
				ProductID:  uint64(firstProduct.ID),
				ChannelID:  uint64(2),
			}
			productEvalResp, err := riskService.EvaluateRiskWithProduct(ctx, productParams)
			if err != nil {
				t.Logf("带产品评估失败(可能是测试环境问题): %v", err)
			} else {
				assert.NotNil(t, productEvalResp)
				t.Logf("带产品评估完成: ProductID=%d, Score=%d, Result=%d, CreditLimit=%.2f",
					firstProduct.ID, productEvalResp.RiskScore, productEvalResp.RiskResult, productEvalResp.AvailableCreditLimit)
			}
		} else {
			t.Log("没有匹配到产品，跳过带产品的评估测试")
		}

		t.Log("集成测试完成")
	})

}

// TestEvaluateRiskWithProduct 测试带产品的风控评估功能
func TestEvaluateRiskWithProduct(t *testing.T) {
	riskService := setupRiskService()
	ctx := context.Background()

	// 测试渠道1特殊处理
	t.Run("渠道1直接通过", func(t *testing.T) {
		params := EvaluateRiskWithProductParams{
			CustomerID: 17,
			ProductID:  1,
			ChannelID:  1,
		}
		resp, err := riskService.EvaluateRiskWithProduct(ctx, params)
		assert.NoError(t, err)
		assert.Equal(t, 1000, resp.RiskScore)
		assert.Equal(t, 0, resp.RiskResult)
		t.Logf("渠道1测试: Score=%d, Result=%d", resp.RiskScore, resp.RiskResult)
	})

	// 测试正常评估流程
	t.Run("正常评估流程", func(t *testing.T) {
		params := EvaluateRiskWithProductParams{
			CustomerID: 17,
			ProductID:  1,
			ChannelID:  2,
		}
		resp, err := riskService.EvaluateRiskWithProduct(ctx, params)
		if err != nil {
			t.Logf("评估失败(可能是测试环境问题): %v", err)
			return
		}
		assert.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.RiskScore, 0)
		assert.LessOrEqual(t, resp.RiskScore, 1000)
		t.Logf("正常评估测试: Score=%d, Result=%d", resp.RiskScore, resp.RiskResult)
	})
}
