{"level":"dev.info","ts":"[2025-08-04 17:10:00.059]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request":"{\"service\":\"fosun.sumpay.api.trade.order.search.merchant\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250804171000\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_no\":\"RP1754297885279933166\"}"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.070]","caller":"sumpay/service.go:470","msg":"发送请求到","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[app_id:s100000040 format:JSON mer_no:s100000040 order_no:RP1754297885279933166 service:fosun.sumpay.api.trade.order.search.merchant sign:HQVY3lxBJt7gku+N0aK/AA5EEz8LyTOQJfhzBaFd4GcJolr0K0WxsrBQuNzOCoN1epOSzlfLtRElqlSfQ47FWiehbkk0X80Gbp5u4cxe2dHiXUmZBxjTSYvpcbu8g8o9IW2vOzHt1wKO4lxkrT8tPPXYevHfxnzXjrH51nKyGfSJWyqmlQ3OQ/FBpEm4ZcFQgdZtEQnWhaf9kNlu1MNTHFHGV7H45TmfgA8a27M0ydvW4119kqVoG2Ar7/8drhf/gAjWdXDjaMMtJnZrJtL4B3skxi67AzMJe3v6h8eckCD1DLOyvAgiB8dzUhUtWaiWcDXhqp5LrvW2kv/FCHcpyw== sign_type:CERT terminal_type:API timestamp:20250804171000 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.261]","caller":"sumpay/service.go:480","msg":"收到响应","status_code":200,"response_body":"{\"resp_code\":\"000000\",\"sign\":\"j8aD8bIBbaP3WCo4U0W/+UTcsW/umqRGyE7irAy1Z7/wgU164nszm/wR+8Tqy+CJ2BsHKw5Kl6E7OS1LXgQQqiE51m6bgoXn1kHCyWVjH/JezzvCKPS3H4A5bvmH/MhEbTE3AH9GVi+D9vyNrAq1gcErBjnnKMzOe77qfYyRyuVEY9JK+vPvPuAqyPkMECNtSTx+dbEOzM0Opo+nkhI/Qh3NLGEh9nuOX1dATCkSy4q2L8/s8A+i5bPwajGzhvyZWPohkeeje5UxeFRlrVTQpSgIvC3HIZq5+WpFoqOa/E5h3l8vKcy8zgaN7lNSaN7gI8iR6UWQsg8OQrRQ1iGZ7A==\",\"resp_msg\":\"处理成功\",\"sign_type\":\"CERT\",\"sumpay_order_search_merchant_response\":\"{\\\"offset_amount\\\":\\\"0\\\",\\\"order_no\\\":\\\"RP1754297885279933166\\\",\\\"order_time\\\":\\\"20250804165805\\\",\\\"paid_amount\\\":\\\"1.02\\\",\\\"pay_product_code\\\":\\\"91401\\\",\\\"payment_type\\\":\\\"00\\\",\\\"serial_no\\\":\\\"*********\\\",\\\"status\\\":\\\"2\\\",\\\"success_time\\\":\\\"0\\\",\\\"trade_amt\\\":1.02,\\\"trade_code\\\":\\\"T0002\\\"}\"}"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.261]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","response":"map[resp_code:000000 resp_msg:处理成功 sign:j8aD8bIBbaP3WCo4U0W/+UTcsW/umqRGyE7irAy1Z7/wgU164nszm/wR+8Tqy+CJ2BsHKw5Kl6E7OS1LXgQQqiE51m6bgoXn1kHCyWVjH/JezzvCKPS3H4A5bvmH/MhEbTE3AH9GVi+D9vyNrAq1gcErBjnnKMzOe77qfYyRyuVEY9JK+vPvPuAqyPkMECNtSTx+dbEOzM0Opo+nkhI/Qh3NLGEh9nuOX1dATCkSy4q2L8/s8A+i5bPwajGzhvyZWPohkeeje5UxeFRlrVTQpSgIvC3HIZq5+WpFoqOa/E5h3l8vKcy8zgaN7lNSaN7gI8iR6UWQsg8OQrRQ1iGZ7A== sign_type:CERT sumpay_order_search_merchant_response:{\"offset_amount\":\"0\",\"order_no\":\"RP1754297885279933166\",\"order_time\":\"20250804165805\",\"paid_amount\":\"1.02\",\"pay_product_code\":\"91401\",\"payment_type\":\"00\",\"serial_no\":\"*********\",\"status\":\"2\",\"success_time\":\"0\",\"trade_amt\":1.02,\"trade_code\":\"T0002\"}]"}
