<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">{{ loginTitle }}</div>
    <div class="login-form-sub-title">
      {{ loginSubTitle }}
    </div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      @submit="handleSubmit"
    >
      <a-form-item
        field="username"
        :rules="[{ required: true, message: $t('login.form.userName.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.username"
          :placeholder="$t('login.form.userName.placeholder')"
        >
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input-password
          v-model="userInfo.password"
          :placeholder="$t('login.form.password.placeholder')"
          allow-clear
        >
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>

      <a-form-item
        field="mobile"
        :rules="[{ required: true, message: '请输入手机号' }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.mobile"
          placeholder="请输入手机号"
        >
          <template #prefix>
            <icon-mobile />
          </template>
        </a-input>
      </a-form-item>

      <a-form-item field="captcha" :rules="[{required: true, message: '请输入图形验证码'}]" :validate-trigger="['change', 'blur']" hide-label>
        <a-input placeholder="请输入图形验证码" allow-clear v-model="userInfo.captcha">
          <template #append>
            <img @click="getCaptchaFun" :src="captcha.captchaImage" width="80"/>
          </template>
        </a-input>
      </a-form-item>

      <a-form-item
        field="code"
        :rules="[{ required: true, message: '请输入短信验证码' }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.code"
          placeholder="请输入短信验证码"
        >
          <template #append>
            <div @click="handleGetCode">{{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}</div>
          </template>
        </a-input>
      </a-form-item>


      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox
            checked="rememberPassword"
            :model-value="loginConfig.rememberPassword"
            @change="setRememberPassword as any"
          >
            {{ $t('login.form.rememberPassword') }}
          </a-checkbox>
          <a-link @click="GoToType('forget')">{{ $t('login.form.forgetPassword') }}</a-link>
        </div>
        <a-button type="primary" html-type="submit" long :loading="loading">
          {{ $t('login.form.login') }}
        </a-button>
        <!-- <a-button type="text" long class="login-form-register-btn">
          {{ $t('login.form.register') }}
        </a-button> -->
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import { LoginData, postSms } from '@/api/user';
  import { getCaptcha } from '@/api/user'
  const emit = defineEmits(['reback'])
  const router = useRouter();
  const { t } = useI18n();
  //获取标题
  const loginTitle = window?.globalConfig.loginTitle
  const loginSubTitle = window?.globalConfig.loginSubTitle
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage('login-config', {
    rememberPassword: true,
    username: '', // 演示默认值-上线环境请赋空值
    password: '', // 默认密码-上线环境请赋空值
  });
  const userInfo = reactive({
    username: loginConfig.value.username,
    password: loginConfig.value.password,
    mobile: '',
    code: '',
    captcha: ''
  });
  const captcha = reactive({
    captchaId: '',
    captchaImage: ''
  })
  onMounted(() => {
    getCaptchaFun()
  })
  // 图形验证码
  function getCaptchaFun () {
    getCaptcha().then(res => {
      captcha.captchaId = res.captchaId;
      captcha.captchaImage = res.captchaImage;
    })
  }

  const countdown  = ref(0);
  function handleGetCode () {
    if(countdown.value > 0) {
      return false
    }
    const mobile = userInfo.mobile;
    const captchaCode = userInfo.captcha;
    const mobilePattern = /^1[3-9]\d{9}$/;

    if (!mobile) {
      return Message.warning('请输入手机号');
    } else if (!mobilePattern.test(mobile)) {
      return Message.warning('手机号格式错误');
    }

    if (!captchaCode) {
      return Message.warning('请填写图形验证码');
    } else if (captchaCode.length !== 5) {
      return Message.warning('图形验证码输入有误，请输入5位验证码');
    }

    postSms({
      mobile: userInfo.mobile,
      captcha: userInfo.captcha,
      captchaId: captcha.captchaId,
    }).then(res => {
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    })
  }

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    console.log(errors);
    console.log(values)
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        await userStore.login(values as LoginData);
        const { redirect, ...othersQuery } = router.currentRoute.value.query;
         var toURl=(redirect as string)
         if(toURl=="notFound"){
          toURl="home"
         }
        router.replace({
          name: toURl || 'home',
          query: {
            ...othersQuery,
          },
        });
        Message.success({content:t('login.form.login.success'),id:"menuNotice"})
        const { rememberPassword } = loginConfig.value;
        const { username, password } = values;
        // 实际生产环境需要进行加密存储。
        // The actual production environment requires encrypted storage.
        loginConfig.value.username = rememberPassword ? username : '';
        loginConfig.value.password = rememberPassword ? password : '';
      } catch (err) {
        errorMessage.value = (err as Error).message;
      } finally {
        setLoading(false);
      }
    }
  };
  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };
  const GoToType=(keys:string)=>{
    emit('reback',keys)
  }
</script>

<style lang="less" scoped>
  .login-form {
    &-wrapper {
      width: 320px;
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
</style>
