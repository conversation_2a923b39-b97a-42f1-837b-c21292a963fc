<template>
  <div class="container">
    <!-- 搜索条件区域 -->
    <a-card class="general-card" title="搜索条件">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        @submit="handleSearch"
      >
        <!-- 第一行：姓名、手机号、身份证 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="name" label="姓名">
              <a-input
                v-model="queryForm.name"
                placeholder="请输入姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="mobile" label="手机号">
              <a-input
                v-model="queryForm.mobile"
                placeholder="请输入手机号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="idCard" label="身份证">
              <a-input
                v-model="queryForm.idCard"
                placeholder="请输入身份证号"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：审核人、渠道来源、是否风控 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="reviewerID" label="审核人">
              <a-select
                v-model="queryForm.reviewerID"
                placeholder="请选择审核人"
                allow-clear
              >
                <a-option
                  v-for="item in reviewerOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="channelId" label="渠道来源">
              <a-select
                v-model="queryForm.channelId"
                placeholder="请选择渠道"
                allow-clear
              >
                <a-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="riskScore" label="风控分数">
              <a-input-group>
                <a-input
                  v-model="queryForm.riskScoreMin"
                  placeholder="最小值"
                  style="width: 45%"
                />
                <a-input
                  style="
                    width: 10%;
                    text-align: center;
                    pointer-events: none;
                    background-color: #f7f8fa;
                  "
                  placeholder="~"
                  readonly
                />
                <a-input
                  v-model="queryForm.riskScoreMax"
                  placeholder="最大值"
                  style="width: 45%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="reminderQuota" label="剩余额度">
              <a-input-group>
                <a-input
                  v-model="queryForm.reminderQuotaMin"
                  placeholder="最小值"
                  style="width: 45%"
                />
                <a-input
                  style="
                    width: 10%;
                    text-align: center;
                    pointer-events: none;
                    background-color: #f7f8fa;
                  "
                  placeholder="~"
                  readonly
                />
                <a-input
                  v-model="queryForm.reminderQuotaMax"
                  placeholder="最大值"
                  style="width: 45%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="identityStatus" label="订单状态">
              <a-select
                v-model="queryForm.identityStatus"
                placeholder="请选择认证状态"
                allow-clear
              >
                <a-option value="0" label="未认证" />
                <a-option value="1" label="认证中" />
                <a-option value="2" label="认证成功" />
                <a-option value="3" label="认证失败" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="isComplaint" label="是否投诉">
              <a-select
                v-model="queryForm.isComplaint"
                placeholder="请选择"
                allow-clear
              >
                <a-option value="1" label="是" />
                <a-option value="0" label="否" />
              </a-select>
            </a-form-item>
          </a-col>

        </a-row>


        <!-- 第七行：设备来源、进件时间、注册时间 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="loanCount" label="放款次数">
              <a-input
                v-model="queryForm.loanCount"
                placeholder="请输入放款次数"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="loanTime" label="进件时间">
              <a-range-picker
                v-model="loanTimeRange"
                style="width: 100%"
                @change="handleLoanTimeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="registerTime" label="注册时间">
              <a-range-picker
                v-model="registerTimeRange"
                style="width: 100%"
                @change="handleRegisterTimeChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第八行：订单状态、放款次数 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="orderStatus" label="订单状态">
              <a-select
                v-model="queryForm.orderStatus"
                placeholder="请选择订单状态"
                allow-clear
              >
                <a-option value="0" label="待放款" />
                <a-option value="1" label="放款中" />
                <a-option value="2" label="交易关闭" />
                <a-option value="3" label="交易完成" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="realNameIncomplete" label="实名未下单">
              <a-select
                v-model="queryForm.realNameIncomplete"
                placeholder="请选择"
                allow-clear
              >
                <a-option value="1" label="是" />
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 操作按钮 -->
        <a-row>
          <a-col :span="24">
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  <template #icon>
                    <icon-search />
                  </template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card class="general-card" title="客户列表">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <div class="table-box" style="height: 60vh">
        <a-table
          row-key="id"
          :loading="loading"
          :pagination="pagination"
          :data="tableData"
          :bordered="false"
          :scroll="{ y: '100%' }"
          size="medium"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <template #columns>
            <a-table-column title="No" :width="60" align="center" fixed="left">
              <template #cell="{ rowIndex }">
                {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
              </template>
            </a-table-column>

            <a-table-column title="渠道名称" data-index="channelName" :width="120" />
            <a-table-column title="审核人" data-index="reviewerName" :width="120" />
            <a-table-column title="客户来源" data-index="deviceSource" :width="100" />
            <a-table-column title="姓名" data-index="name" :width="100" />
            <a-table-column title="手机号" data-index="mobile" :width="130" />
            <a-table-column title="用户备注" data-index="userRemark" :width="150" ellipsis tooltip />
            <a-table-column title="注册时间" data-index="createTime" :width="200" />
            <a-table-column title="放款次数" data-index="loanCount" :width="100" align="center" />
            <a-table-column title="认证状态" data-index="identityStatusText" :width="120" />
            <a-table-column title="订单状态" data-index="orderStatus" :width="100" >
              <template #cell="{ record }">
                {{ record.totalOrderCount <= 0?'未借款':formatOrderStatus(record.orderStatus) }}
              </template>
            </a-table-column>
            <a-table-column title="在借订单数" data-index="borrowingOrderCount" :width="120" />
            <a-table-column title="历史订单总数" data-index="totalOrderCount" :width="140" />
            <a-table-column title="进件时间" data-index="loanTime" :width="200" />
            <a-table-column title="最后还款时间" data-index="lastRepayTime" :width="200" />
            <a-table-column title="账单到期时间" data-index="billDueTime" :width="200" />
            <a-table-column title="投诉状态" data-index="complaintStatusText" :width="100" />
            <a-table-column title="总额度" data-index="allQuota" :width="120" align="right">
              <template #cell="{ record }">
                {{ formatCurrency(record.allQuota) }}
              </template>
            </a-table-column>
            <a-table-column title="剩余额度" data-index="reminderQuota" :width="120" align="right">
              <template #cell="{ record }">
                {{ formatCurrency(record.reminderQuota) }}
              </template>
            </a-table-column>
            <a-table-column title="模型分" data-index="riskScore" :width="100" align="center" />
            <a-table-column title="身份证" :width="180">
              <template #cell="{ record }">
                <a-tooltip v-if="record.idCardMasked && record.idCardMasked != 'null'" :content="record.idCardFull">
                <span style="cursor: pointer; color: #1890ff;">
                  {{ record.idCardMasked }}
                </span>
                </a-tooltip>
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="220" fixed="right" align="center">
              <template #cell="{ record }">
                <div class="action-buttons">
                  <div class="action-row">
                    <a-button size="small" @click="handleViewOrder(record)">查看订单</a-button>
                    <a-button size="small" @click="handleViewDetail(record)">处理</a-button>
                    <a-button size="small" @click="handleEditRemark(record)">编辑备注</a-button>
                  </div>
                  <div class="action-row">
                    <a-button size="small" @click="handleViewRemark(record)">查看备注</a-button>
                    <a-button size="small" @click="handleUnlockCustomer(record)">解除注销</a-button>
                  </div>
                </div>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

    </a-card>

    <!-- 编辑备注弹窗 -->
    <a-modal
      v-model:visible="remarkModalVisible"
      title="编辑用户备注"
      @ok="handleRemarkSubmit"
      @cancel="handleRemarkCancel"
    >
      <a-form ref="remarkFormRef" :model="remarkForm" layout="vertical">
        <a-form-item field="userRemark" label="用户备注" :rules="[{ required: true, message: '请输入用户备注' }]">
          <a-textarea
            v-model="remarkForm.userRemark"
            placeholder="请输入用户备注"
            :max-length="500"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看备注弹窗 -->
    <a-modal
      v-model:visible="viewRemarkModalVisible"
      title="查看用户备注"
      :footer="false"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="用户ID">{{ currentCustomer?.id }}</a-descriptions-item>
        <a-descriptions-item label="用户备注">{{ currentCustomer?.userRemark || '暂无备注' }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ currentCustomer?.createTime }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 客户详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="客户详情"
      width="800px"
      :footer="false"
    >
      <a-spin :loading="detailLoading">
        <div v-if="customerDetail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="客户ID">{{ customerDetail.id }}</a-descriptions-item>
            <a-descriptions-item label="姓名">{{ customerDetail.name }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ customerDetail.mobile }}</a-descriptions-item>
            <a-descriptions-item label="身份证">{{ customerDetail.idCardFull }}</a-descriptions-item>
            <a-descriptions-item label="渠道名称">{{ customerDetail.channelName }}</a-descriptions-item>
            <a-descriptions-item label="审核人">{{ customerDetail.reviewerName }}</a-descriptions-item>
            <a-descriptions-item label="设备来源">{{ customerDetail.deviceSource }}</a-descriptions-item>
            <a-descriptions-item label="风控流水号">{{ customerDetail.riskFlowNumber || '无' }}</a-descriptions-item>
            <a-descriptions-item label="风控分数">{{ customerDetail.riskScore || '无' }}</a-descriptions-item>
            <a-descriptions-item label="认证状态">{{ customerDetail.identityStatusText }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">{{ customerDetail.orderStatusText }}</a-descriptions-item>
            <a-descriptions-item label="投诉状态">{{ customerDetail.complaintStatusText }}</a-descriptions-item>
            <a-descriptions-item label="总额度">{{ formatCurrency(customerDetail.allQuota) }}</a-descriptions-item>
            <a-descriptions-item label="剩余额度">{{ formatCurrency(customerDetail.reminderQuota) }}</a-descriptions-item>
            <a-descriptions-item label="放款次数">{{ customerDetail.loanCount }}</a-descriptions-item>
            <a-descriptions-item label="进件时间">{{ customerDetail.loanTime }}</a-descriptions-item>
            <a-descriptions-item label="注册时间">{{ customerDetail.createTime }}</a-descriptions-item>
            <a-descriptions-item label="用户备注" :span="2">
              <div style="max-height: 100px; overflow-y: auto;">
                {{ customerDetail.userRemark || '暂无备注' }}
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
} from '@arco-design/web-vue/es/icon';
import type {
  CustomerQueryParams,
  CustomerListItem,
  CustomerOptions,
} from '@/api/usermanage';
import {
  getCustomerList,
  getCustomerOptions,
  updateCustomerRemark,
  getCustomerDetail,
  unlockCustomer,
} from '@/api/usermanage';
import router from '@/router';

// 响应式数据
const loading = ref(false);
const tableData = ref<CustomerListItem[]>([]);
const reviewerOptions = ref<{ value: string; label: string }[]>([]);
const channelOptions = ref<{ value: string; label: string }[]>([]);

// 查询表单
const queryFormRef = ref();
const queryForm = reactive<CustomerQueryParams>({
  name: '',
  mobile: '',
  idCard: '',
  reviewerID: '',
  channelId: '',
  isRisk: '',
  riskFlowNumber: '',
  riskScoreMin: '',
  riskScoreMax: '',
  reminderQuotaMin: '',
  reminderQuotaMax: '',
  identityStatus: '',
  identitySubStatus: '',
  orderStatus: '',
  registerIncomplete: '',
  realNameIncomplete: '',
  hasQuotaNoOrder: '',
  isNewUser: '',
  isComplaint: '',
  userRemark: '',
  loanCount: '',
  deviceSource: '',
  loanTimeStart: '',
  loanTimeEnd: '',
  registerTimeStart: '',
  registerTimeEnd: '',
  page: 1,
  pageSize: 20,
});

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 认证状态级联选择
const identityStatusValue = ref<string[]>([]);
const identityStatusOptions = ref([
  {
    value: '1',
    label: '申请前',
    children: [
      { value: '1', label: '注册通过' },
      { value: '2', label: 'ORC通过' },
      { value: '3', label: '活体通过' },
      { value: '4', label: '联系人通过' },
    ],
  },
  {
    value: '2',
    label: '等待申请结果',
    children: [
      { value: '1', label: '待人审' },
      { value: '2', label: '等待分控回调' },
      { value: '3', label: '等待分控' },
    ],
  },
  {
    value: '3',
    label: '申请通过',
    children: [
      { value: '1', label: '审核通过' },
      { value: '2', label: '冻结' },
      { value: '3', label: '风控过期' },
    ],
  },
  {
    value: '4',
    label: '申请不通过',
    children: [
      { value: '1', label: '风控未通过' },
      { value: '2', label: '审核未通过' },
      { value: '3', label: '风控异常' },
    ],
  },
]);

// 时间范围
const loanTimeRange = ref<string[]>([]);
const registerTimeRange = ref<string[]>([]);

// 备注弹窗
const remarkModalVisible = ref(false);
const remarkFormRef = ref();
const remarkForm = reactive({
  userRemark: '',
});
const currentCustomer = ref<CustomerListItem | null>(null);

// 查看备注弹窗
const viewRemarkModalVisible = ref(false);

// 初始化
onMounted(() => {
  // 先设置一些测试数据
  reviewerOptions.value = [
    { value: '1', label: '审核员A' },
    { value: '2', label: '审核员B' }
  ];
  channelOptions.value = [
    { value: '1', label: '渠道A' },
    { value: '2', label: '渠道B' }
  ];
  

  
  fetchOptions();
  fetchData();
});

// 获取筛选选项
const fetchOptions = async () => {
  try {
    const response = await getCustomerOptions();
    
    // 根据实际响应格式处理：直接返回 { reviewerOptions: [...], channelOptions: [...] }
    if (response && response.reviewerOptions && response.channelOptions) {
      
      // 使用 nextTick 确保响应式更新
      await nextTick();
      reviewerOptions.value = [...response.reviewerOptions];
      channelOptions.value = [...response.channelOptions];
      
      // 强制触发响应式更新
      await nextTick();
    } else {
      console.warn('响应格式不正确:', response);
      reviewerOptions.value = [];
      channelOptions.value = [];
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error);
    reviewerOptions.value = [];
    channelOptions.value = [];
  }
};

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params = {
      ...queryForm,
      page: pagination.current,
      pageSize: pagination.pageSize,
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === undefined || params[key] === null) {
        delete params[key];
      }
    });

    const response = await getCustomerList(params);

    if (response && response.list) {
      // 实际返回格式是 { list: [...], total: number, ... }
      const { list: listData, total: totalCount } = response;
      tableData.value = Array.isArray(listData) ? listData : [];
      pagination.total = totalCount || 0;
    } else {
      console.error('响应格式错误:', response);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取客户列表失败:', error);
    Message.error('获取客户列表失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置
const handleReset = () => {
  queryFormRef.value?.resetFields();
  identityStatusValue.value = [];
  loanTimeRange.value = [];
  registerTimeRange.value = [];
  pagination.current = 1;
  queryForm.loanTimeStart = '';
  queryForm.loanTimeEnd= '';
  queryForm.registerTimeStart = '';
  queryForm.registerTimeEnd = '';
  fetchData();
};

// 刷新
const handleRefresh = () => {
  fetchData();
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

// 认证状态变化
const handleIdentityStatusChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.identityStatus = value[0];
    queryForm.identitySubStatus = value[1];
  } else {
    queryForm.identityStatus = '';
    queryForm.identitySubStatus = '';
  }
};

// 时间范围变化
const handleLoanTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.loanTimeStart = value[0];
    queryForm.loanTimeEnd = value[1];
  } else {
    queryForm.loanTimeStart = '';
    queryForm.loanTimeEnd = '';
  }
};

const handleRegisterTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.registerTimeStart = value[0];
    queryForm.registerTimeEnd = value[1];
  } else {
    queryForm.registerTimeStart = '';
    queryForm.registerTimeEnd = '';
  }
};

// 客户详情弹窗
const detailModalVisible = ref(false);
const detailLoading = ref(false);
const customerDetail = ref<CustomerListItem | null>(null);

// 操作处理
const handleView = async (record: CustomerListItem) => {
  try {
    detailLoading.value = true;
    detailModalVisible.value = true;
    const { data } = await getCustomerDetail(record.id);
    customerDetail.value = data;
  } catch (error) {
    Message.error('获取客户详情失败');
  } finally {
    detailLoading.value = false;
  }
};

const handleViewOrder = (record: CustomerListItem) => {
  if(record.totalOrderCount == 0) {
    return Message.warning('该客户无订单信息');
  }
  router.push({
    path: '/ordermanagement/Orderlist',
    query: {
      user_id_card: record.idCardFull
    }
  })
}

// 处理按钮-跳转用户详情页
const handleViewDetail = async (record: CustomerListItem) => {
  router.push({path: `/usermanage/userDetail/${record.id}`});
}

const handleEditRemark = (record: CustomerListItem) => {
  currentCustomer.value = record;
  remarkForm.userRemark = record.userRemark;
  remarkModalVisible.value = true;
};

const handleViewRemark = (record: CustomerListItem) => {
  currentCustomer.value = record;
  viewRemarkModalVisible.value = true;
};



// 备注提交
const handleRemarkSubmit = async () => {
  try {
    await remarkFormRef.value?.validate();
    if (currentCustomer.value) {
      await updateCustomerRemark(currentCustomer.value.id, {
        userRemark: remarkForm.userRemark,
      });
      Message.success('更新备注成功');
      remarkModalVisible.value = false;
      fetchData();
    }
  } catch (error) {
    Message.error('更新备注失败');
  }
};

const handleRemarkCancel = () => {
  remarkModalVisible.value = false;
  remarkForm.userRemark = '';
};

// 解除注销
const handleUnlockCustomer = async (record: CustomerListItem) => {
  try {
    await unlockCustomer(record.id);
    Message.success('解除注销成功');
    fetchData(); // 刷新列表
  } catch (error) {
    Message.error('解除注销失败');
  }
};

// 工具函数
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

const formatOrderStatus = (status: number) => {
  const dict = [
    { code: 0, name: "待放款" },
    { code: 1, name: "放款中" },
    { code: 2, name: "交易关闭" },
    { code: 3, name: "交易完成" },
  ]
  let items = dict.filter((item: any) => item.code === status);
  return items.length?items[0].name: '';
}
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  width: 100%;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-row .arco-btn {
  margin: 1px;
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
  line-height: 20px;
}
</style>