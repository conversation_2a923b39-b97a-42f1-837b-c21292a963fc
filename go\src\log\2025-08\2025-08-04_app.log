{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.335]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.394]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.620]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.691]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.865]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.874]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.670]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.707]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.252]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.262]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.595]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.627]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.616]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.654]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.954]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:41:41.003]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.779]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.814]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.400]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.412]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.599]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.602]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.641]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.error","ts":"[2025-08-04 14:55:51.635]","caller":"runtime/proc.go:283","msg":"启动定时任务调度器失败: 启动调度引擎失败: 注册任务失败: 创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"runtime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.error","ts":"[2025-08-04 14:56:00.981]","caller":"runtime/proc.go:283","msg":"启动定时任务调度器失败: 启动调度引擎失败: 注册任务失败: 创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"runtime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.190]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.214]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.227]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.049]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.073]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.084]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.145]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.169]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.183]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.467]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.473]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.991]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:58:56.037]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.810]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.818]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.197]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.276]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.208]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.216]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.088]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.129]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.166]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.377]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.396]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.404]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.863]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.886]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.896]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:03:26.355]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取渠道统计列表失败","data":"\"获取渠道统计列表失败: 查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist\""}
{"level":"dev.info","ts":"[2025-08-04 15:04:22.509]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取渠道统计列表失败","data":"\"获取渠道统计列表失败: 查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist\""}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.547]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.564]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.570]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:07:26.535]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取渠道统计列表失败","data":"\"获取渠道统计列表失败: 查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist\""}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.484]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.688]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:14:36.822]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取渠道统计列表失败","data":"\"获取渠道统计列表失败: 查询渠道统计列表失败: 获取总记录数失败: sql: expected 2 arguments, got 1\""}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.030]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.047]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.079]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.068]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.086]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.226]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.636]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.639]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.670]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.318]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.359]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.399]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.433]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.752]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.769]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.829]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.779]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.850]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.448]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.492]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.706]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.726]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.789]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.081]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.120]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.210]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.269]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.171]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.173]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.215]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.145]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.183]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.474]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.477]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.533]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.610]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.870]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.960]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.038]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.659]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.714]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.343]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.399]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.221]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.279]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.162]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.220]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.233]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.304]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.780]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.850]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.809]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.812]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.873]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.235]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.273]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.124]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.154]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.968]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.988]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.636]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.670]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.245]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.262]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.538]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.559]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.616]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.632]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
