{"level":"dev.info","ts":"[2025-08-05 09:10:35.497]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.499]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.499]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.499]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.504]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.511]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.450]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.451]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.451]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.451]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.453]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.454]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.454]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.454]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.454]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.268]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.269]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.269]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.269]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.271]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.272]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.272]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.272]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:26:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 67 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2022640?, 0x324a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000dd02e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000fa20e0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000fa20e0, {0x29e4608, 0xc0007a81c0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc000054200)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc000054200)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x29e7768, 0xc000fa20e0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1f8b4e0, 0xc000f94330, 0x13}, {0x21c27d0, 0x4}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1f8b4e0, 0xc000f94330, 0x13}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1f8b4e0, 0xc000f94330}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x29e4598, 0xc000fa84b0}, {0x29e4598, 0xc000e38190}, 0xc000f90340, {0x83, 0x6e, 0xda, 0xf6, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x29e4598, 0xc000fa84b0}, {0x29e4598, 0xc000e38190}, 0xc000f90340, {0x83, 0x6e, 0xda, 0xf6, 0xc4, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 47\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0015844,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:26:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"repayment-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 30 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2022640?, 0x324a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000d44238, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions(0xc000f941e0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188 +0xf6\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute(0xc000f941e0, {0x29e4608, 0xc0007a6700})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65 +0x4d5\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc000be0d80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc000be0d80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x29e77c0, 0xc000f941e0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1f8b4e0, 0xc000f943c0, 0x13}, {0x21c27d0, 0x4}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1f8b4e0, 0xc000f943c0, 0x13}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1f8b4e0, 0xc000f943c0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x29e4598, 0xc000fa85a0}, {0x29e4598, 0xc000e38190}, 0xc000f904b0, {0x6d, 0xc1, 0x3c, 0x87, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x29e4598, 0xc000fa85a0}, {0x29e4598, 0xc000e38190}, 0xc000f904b0, {0x6d, 0xc1, 0x3c, 0x87, 0x76, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 48\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.0021294,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:26:00.003]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 66 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2022640?, 0x324a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000e64170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000fa2020)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000fa2020, {0x29e4608, 0xc000c0a540})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc000830200)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc000830200)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x29e76b8, 0xc000fa2020})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1f8b4e0, 0xc000f94480, 0x13}, {0x21c27d0, 0x4}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1f8b4e0, 0xc000f94480, 0x13}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1f8b4e0, 0xc000f94480}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x29e4598, 0xc000fa8780}, {0x29e4598, 0xc000e38190}, 0xc000f90790, {0x85, 0x45, 0xb8, 0xfc, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x29e4598, 0xc000fa8780}, {0x29e4598, 0xc000e38190}, 0xc000f90790, {0x85, 0x45, 0xb8, 0xfc, 0xdf, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 49\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.003]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0026498,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.003]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:26:00.003]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"disbursement-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 29 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2022640?, 0x324a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x29e5f88, 0xc000812440})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000812440)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000e722f8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions(0xc000fa2080)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute(0xc000fa2080, {0x29e4608, 0xc000c0af50})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57 +0x385\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc000830400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc000830400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x29e7710, 0xc000fa2080})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1f8b4e0, 0xc000f944e0, 0x13}, {0x21c27d0, 0x4}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1f8b4e0, 0xc000f944e0, 0x13}, {0x32f4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1f8b4e0, 0xc000f944e0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x29e4598, 0xc000fa8870}, {0x29e4598, 0xc000e38190}, 0xc000f90910, {0xae, 0x5c, 0x5e, 0x25, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x29e4598, 0xc000fa8870}, {0x29e4598, 0xc000e38190}, 0xc000f90910, {0xae, 0x5c, 0x5e, 0x25, 0xb5, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 46\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.003]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0010654,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:26:00.003]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.464]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.465]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.465]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.465]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.467]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.468]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.468]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.468]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:27:00.003]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 26 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2182640?, 0x33aa120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000099140)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2b45fc8, 0xc000099140})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2b45fc8, 0xc000099140})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000099140)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000e722e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000e37640)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000e37640, {0x2b44648, 0xc00081c150})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc00077e680)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc00077e680)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x2b477a8, 0xc000e37640})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x20eb4e0, 0xc000c04180, 0x13}, {0x23227d0, 0x4}, {0x3454c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x20eb4e0, 0xc000c04180, 0x13}, {0x3454c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x20eb4e0, 0xc000c04180}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2b445d8, 0xc000e385f0}, {0x2b445d8, 0xc000e38190}, 0xc000c06330, {0x91, 0xda, 0x6e, 0xa1, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2b445d8, 0xc000e385f0}, {0x2b445d8, 0xc000e38190}, 0xc000c06330, {0x91, 0xda, 0x6e, 0xa1, 0x8b, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 25\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.003]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0022846,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.003]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:27:00.012]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 48 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2182640?, 0x33aa120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000099140)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2b45fc8, 0xc000099140})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2b45fc8, 0xc000099140})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000099140)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc001016170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000e375a0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000e375a0, {0x2b44648, 0xc00081a540})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000e6c140, 0xc000e2e480)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000e6c140, 0xc000e2e480)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000e6c140, {0x2b476f8, 0xc000e375a0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x20eb4e0, 0xc000c040c0, 0x13}, {0x23227d0, 0x4}, {0x3454c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x20eb4e0, 0xc000c040c0, 0x13}, {0x3454c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x20eb4e0, 0xc000c040c0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2b445d8, 0xc000e38140}, {0x2b445d8, 0xc000e38190}, 0xc000c06040, {0x63, 0x5, 0x1d, 0x25, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2b445d8, 0xc000e38140}, {0x2b445d8, 0xc000e38190}, 0xc000c06040, {0x63, 0x5, 0x1d, 0x25, 0x20, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 47\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.013]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0110832,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:27:00.013]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.373]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.374]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.374]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.374]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.376]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.377]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.377]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.377]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.377]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.685]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.687]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.687]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.687]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:29:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 16 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x1bf2640?, 0x2e1a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000e86b80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x25b5f88, 0xc000e86b80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x25b5f88, 0xc000e86b80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000e86b80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000d50170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000d5fac0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000d5fac0, {0x25b4608, 0xc000792000})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000ce0800, 0xc0008b2980)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000ce0800, 0xc0008b2980)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000ce0800, {0x25b76b8, 0xc000d5fac0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1b5b4e0, 0xc000da4660, 0x13}, {0x1d927d0, 0x4}, {0x2ec4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1b5b4e0, 0xc000da4660, 0x13}, {0x2ec4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1b5b4e0, 0xc000da4660}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x25b4598, 0xc000da6960}, {0x25b4598, 0xc000da6050}, 0xc000da2de0, {0x8b, 0xa6, 0xbe, 0xb, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x25b4598, 0xc000da6960}, {0x25b4598, 0xc000da6050}, 0xc000da2de0, {0x8b, 0xa6, 0xbe, 0xb, 0x16, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 14\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0016095,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:29:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 47 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x1bf2640?, 0x2e1a120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000e86b80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x25b5f88, 0xc000e86b80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x25b5f88, 0xc000e86b80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000e86b80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000d3a2e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000d5fb60)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000d5fb60, {0x25b4608, 0xc000bce310})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000ce0800, 0xc000df8180)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000ce0800, 0xc000df8180)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000ce0800, {0x25b7768, 0xc000d5fb60})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x1b5b4e0, 0xc000da4540, 0x13}, {0x1d927d0, 0x4}, {0x2ec4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x1b5b4e0, 0xc000da4540, 0x13}, {0x2ec4c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1b5b4e0, 0xc000da4540}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x25b4598, 0xc000da6690}, {0x25b4598, 0xc000da6050}, 0xc000da2990, {0xd, 0xa0, 0x33, 0xfa, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x25b4598, 0xc000da6690}, {0x25b4598, 0xc000da6050}, 0xc000da2990, {0xd, 0xa0, 0x33, 0xfa, 0xd7, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 15\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0016095,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:29:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.115]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.115]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.115]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.115]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.116]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.926]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.927]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.927]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.927]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.928]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 83 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000f482e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000dfa520)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000dfa520, {0x2c533a8, 0xc0008f27e0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000f14980)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000f14980)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56508, 0xc000dfa520})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d05350, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d05350, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d05350}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, 0xf, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 49\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0010408,"retry_count":0,"success":true}
{"level":"dev.error","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"disbursement-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 31 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc0010fa2f8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions(0xc000dfa4c0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute(0xc000dfa4c0, {0x2c533a8, 0xc0008feee0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57 +0x385\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000054400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000054400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c564b0, 0xc000dfa4c0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d052c0, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d052c0, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d052c0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8780}, {0x2c53338, 0xc000da8230}, 0xc0007b7280, {0xf9, 0x3a, 0xf6, 0x82, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8780}, {0x2c53338, 0xc000da8230}, 0xc0007b7280, {0xf9, 0x3a, 0xf6, 0x82, 0xd9, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 47\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.0010408,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 68 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc001134170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000dfa480)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000dfa480, {0x2c533a8, 0xc0008f1c70})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000ce5c00)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000ce5c00)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56458, 0xc000dfa480})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000df4420, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000df4420, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000df4420}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, 0xe7, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 82\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0015454,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:30:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"repayment-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 32 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000f76238, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions(0xc000d051d0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188 +0xf6\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute(0xc000d051d0, {0x2c533a8, 0xc0008f3570})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65 +0x4d5\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000f14b80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000f14b80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56560, 0xc000d051d0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d053b0, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d053b0, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d053b0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8960}, {0x2c53338, 0xc000da8230}, 0xc0007b7560, {0x75, 0x7b, 0xf1, 0xd0, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8960}, {0x2c53338, 0xc000da8230}, 0xc0007b7560, {0x75, 0x7b, 0xf1, 0xd0, 0x13, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 48\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.000517,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:30:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:31:00.000]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 70 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc0013a42e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000dfa520)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000dfa520, {0x2c533a8, 0xc000928bd0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000f14d80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000f14d80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56508, 0xc000dfa520})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d05350, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d05350, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d05350}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, 0xf, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 103\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:31:00.000]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 104 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc001198170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000dfa480)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000dfa480, {0x2c533a8, 0xc000927dc0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000ce5e80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000ce5e80)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56458, 0xc000dfa480})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000df4420, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000df4420, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000df4420}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, 0xe7, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 102\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0005035,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:31:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:32:00.000]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 75 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000a3e2e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000dfa520)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000dfa520, {0x2c533a8, 0xc00093a0e0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000f14080)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000f14080)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56508, 0xc000dfa520})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d05350, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d05350, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d05350}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8870}, {0x2c53338, 0xc000da8230}, 0xc0007b73f0, {0x2c, 0xa9, 0x51, 0x98, 0xf, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 74\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:32:00.001]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 76 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000a3e170, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0xc000dfa480)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186 +0xf6\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000dfa480, {0x2c533a8, 0xc00093a1c0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x575\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000f14280)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000f14280)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56458, 0xc000dfa480})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000df4420, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000df4420, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000df4420}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000172370}, {0x2c53338, 0xc000da8230}, 0xc000d06460, {0xf3, 0x5c, 0xc9, 0xf5, 0xe7, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 89\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:186\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":10,"duration":0.0008873,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.001]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-05 09:32:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"disbursement-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 90 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000a5a2f8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions(0xc000dfa4c0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute(0xc000dfa4c0, {0x2c533a8, 0xc0009281c0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57 +0x385\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc00119c080)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc00119c080)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c564b0, 0xc000dfa4c0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d052c0, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d052c0, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d052c0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8780}, {0x2c53338, 0xc000da8230}, 0xc0007b7280, {0xf9, 0x3a, 0xf6, 0x82, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8780}, {0x2c53338, 0xc000da8230}, 0xc0007b7280, {0xf9, 0x3a, 0xf6, 0x82, 0xd9, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 87\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).getPendingDisbursementOrdersWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*DisbursementStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/disbursement_status_sync_compensation.go:57\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:32:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"repayment-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 91 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x2291640?, 0x34b8120?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x2c54d28, 0xc000deec80})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc000deec80)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000ae2238, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions(0xc000d051d0)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188 +0xf6\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute(0xc000d051d0, {0x2c533a8, 0xc0008ff110})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65 +0x4d5\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000a98140, 0xc000054400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000a98140, 0xc000054400)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000a98140, {0x2c56560, 0xc000d051d0})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x21fa4e0, 0xc000d053b0, 0x13}, {0x24317d0, 0x4}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21fa4e0, 0xc000d053b0, 0x13}, {0x3562c80, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21fa4e0, 0xc000d053b0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x2c53338, 0xc000da8960}, {0x2c53338, 0xc000da8230}, 0xc0007b7560, {0x75, 0x7b, 0xf1, 0xd0, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x2c53338, 0xc000da8960}, {0x2c53338, 0xc000da8230}, 0xc0007b7560, {0x75, 0x7b, 0xf1, 0xd0, 0x13, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 88\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).getPendingRepaymentBillsWithSubmittedTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:188\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:65\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":8,"duration":0.0016315,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.0016315,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:32:00.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.736]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.737]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.737]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.737]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.739]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.740]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.741]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.741]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.741]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.657]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.658]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.658]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.658]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.659]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.660]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.660]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.700]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.701]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.701]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.701]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.702]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.666]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.668]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.668]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.668]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.669]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.670]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.670]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.670]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.670]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.017]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0169611,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.017]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:44:00.035]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.035]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0516596,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.053]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.011]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.012]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.012]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.012]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.020]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.034]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0336067,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.034]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.076]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0754389,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:45:00.076]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.886]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.889]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.889]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.889]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.890]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:46:04.761]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:46:04.761]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.177]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.178]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.178]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.178]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.179]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.180]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.181]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.182]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.182]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.183]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.183]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.183]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.183]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.196]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.196]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.196]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.196]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.215]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0190248,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.215]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:48:05.244]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.244]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.260]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0635214,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.260]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.640]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":6.4436552,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.640]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:48:35.249]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:48:35.249]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0186551,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.019159,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:49:00.019]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:49:05.275]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:49:05.275]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 09:49:35.282]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 09:49:35.282]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":90.0866531,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:49:35.282]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.011]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.01099,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.011]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:50:00.042]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.042]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":9,"duration":0.0479831,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.048]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":8,"duration":0.8671589,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:50:30.047]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:50:30.047]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":11,"duration":0.0439441,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.044]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.0461339,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.046]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:51:00.056]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:51:00.056]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 09:51:30.076]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 09:51:30.076]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":90.0758335,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:51:30.076]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:52:00.033]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.033]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":15,"duration":0.0381432,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":16,"duration":0.0381432,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":14,"duration":0.8184559,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:52:30.045]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:52:30.045]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":18,"duration":0.0516527,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.052]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.060]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":17,"duration":0.0600561,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.060]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:53:00.075]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:53:00.075]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 09:53:30.082]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 09:53:30.082]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":13,"duration":90.082186,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:53:30.082]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:54:00.031]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.031]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.042]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":22,"duration":0.0416055,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.042]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":21,"duration":0.057335,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":20,"duration":0.9034796,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:54:30.067]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:54:30.067]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":23,"duration":0.046488,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":24,"duration":0.046488,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.046]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:55:00.077]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:55:00.077]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 09:55:30.089]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 09:55:30.089]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":19,"duration":90.0889414,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:55:30.089]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:56:00.084]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.084]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.084]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":28,"duration":0.0841857,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.084]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.088]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":25,"duration":0.0885825,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.088]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":26,"duration":0.8573706,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:56:30.106]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:56:30.106]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":29,"duration":0.0428852,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":30,"duration":0.0428852,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.043]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:57:00.138]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:57:00.138]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 09:57:30.145]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 09:57:30.145]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":27,"duration":90.1445104,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:57:30.145]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:58:00.045]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.045]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":33,"duration":0.0447316,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.045]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":32,"duration":0.053829,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.932]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":31,"duration":0.9324035,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.933]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:58:41.635]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:58:41.635]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":36,"duration":0.0635797,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.064]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":35,"duration":0.0635797,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 09:59:00.064]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 09:59:11.641]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 09:59:11.641]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.849]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.850]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.574]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.575]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.575]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.575]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.576]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.489]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.490]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.490]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.491]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.492]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.493]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.493]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.493]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.493]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.494]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.494]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.494]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.494]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:18:00.010]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.010]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.063]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0631856,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.063]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0636911,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.064]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.280]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":11.279093,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.280]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:18:30.019]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:18:30.019]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0500956,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0500956,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.050]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:19:00.051]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:19:00.051]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 11:19:30.062]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 11:19:30.062]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":1,"duration":90.0622668,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:19:30.062]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:20:00.059]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.059]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":10,"duration":0.0690561,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0704226,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.071]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.587]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.5849722,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.587]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.222]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.223]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.223]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.223]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.224]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.032]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0317132,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.032]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.069]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0689636,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:21:00.069]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.118]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.120]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.120]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.120]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.121]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.121]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.122]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.123]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.123]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.123]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.556]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.556]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0504281,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0509328,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:23:28.607]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.021]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.020659,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.021]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.021]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.020659,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.021]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:24:00.058]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.058]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.510]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.5103276,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.510]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:24:30.067]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:24:30.067]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0414326,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0414326,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.041]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:25:00.071]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:25:00.071]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 11:25:30.077]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 11:25:30.077]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":5,"duration":90.077278,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:25:30.078]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:26:00.051]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.051]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.070]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":11,"duration":0.0698223,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.070]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.126]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.1255431,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.126]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.524]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.5242218,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.524]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.440]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.441]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.441]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.441]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.443]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.444]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.444]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.444]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.444]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.012]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0113636,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.012]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0424366,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:27:00.043]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:28:00.041]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.041]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0655907,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0653254,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.066]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.790]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.7893022,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.790]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:28:30.046]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:28:30.046]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0463942,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0463942,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.047]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:29:00.074]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:29:00.075]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.006]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.009]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.009]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.009]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.010]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.848]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.849]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.849]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.849]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.851]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.130]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.132]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.132]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.132]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.135]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.136]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.137]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.137]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.137]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.138]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.138]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.139]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.139]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.139]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0162561,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.016]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.035]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0352177,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:37:00.035]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.2590423,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.2590423,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.259]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:38:00.259]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.260]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.322]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":6,"duration":1.3201892,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.322]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:38:30.266]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:38:30.266]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.7509852,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.7509852,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:39:00.751]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:39:00.751]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.113]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.114]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.114]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.114]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.114]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:40:00.021]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.021]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.102]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.1007413,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.102]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.102]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0994962,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.102]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.621]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.6200473,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.621]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.924]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.926]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.926]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.926]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.928]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.928]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.928]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.929]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.930]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.930]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.930]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.062]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0613353,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.062]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.131]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.1308263,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:41:00.131]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:42:00.054]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.054]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0562321,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.057]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0665783,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.067]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.587]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.5861407,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.587]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:42:30.061]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:42:30.061]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.413]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.414]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.414]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.414]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.416]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0498351,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.051]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.088]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0870989,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:43:00.088]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:44:00.050]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.050]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.067145,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.067145,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.067]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.549]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.5493073,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.549]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.324]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.328]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.328]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.328]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.329]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.330]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.330]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.473]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.474]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.474]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.474]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.476]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.477]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.477]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.477]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.480]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.480]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.480]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.482]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.254]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.255]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.255]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.255]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:48:00.015]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.015]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0180369,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0185635,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.018]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0191015,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:48:00.019]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:48:30.017]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:48:30.017]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.009]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0085445,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.009]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.010]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0096163,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.010]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:49:00.021]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:49:00.021]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 11:49:30.023]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 11:49:30.023]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":90.0212883,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:49:30.023]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:50:00.009]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.009]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.009]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":9,"duration":0.0092222,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.009]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.010]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":8,"duration":0.0097639,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.010]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.011]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0114493,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:50:00.011]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:50:30.012]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:50:30.012]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":12,"duration":0.0159955,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.017]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":11,"duration":0.0201661,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.021]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:51:00.025]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:51:00.025]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.565]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.566]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.566]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.566]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.095]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0949482,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.095]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.158]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.1569228,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.158]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:52:00.158]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.158]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.804]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.8035937,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.804]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:52:30.171]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":1,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:52:30.171]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":2,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.123]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.1234197,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.123]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.132]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.1324797,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.132]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:53:00.178]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":2,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:53:00.178]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":3,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 11:53:30.186]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":3,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.error","ts":"[2025-08-05 11:53:30.186]","caller":"engine/executor.go:373","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":90.1851088,"retry_count":3,"success":false,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).logExecutionResult\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:373\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:95\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:53:30.186]","caller":"engine/scheduler.go:191","msg":"任务执行失败","task_name":"disbursement-status-sync-compensation","error":"任务执行失败: 查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:191\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:54:00.064]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.064]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.072]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0716828,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.072]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":10,"duration":0.0794646,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.584]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.5838564,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.584]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.297]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.302]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.302]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.302]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.261]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.262]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.262]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.262]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.263]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.263]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.264]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.265]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.265]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0193238,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.019]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 11:56:00.044]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.044]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0552944,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.563]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.5631276,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.563]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:02:03.998]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 12:02:03.998]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 12:02:03.998]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 12:02:03.998]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.007]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.012]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0121428,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.012]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0657881,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:03:00.066]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0496717,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.058]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":6,"duration":0.0571998,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.058]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.058]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0577041,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.058]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.488]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.4880713,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.488]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0542159,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0542159,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 12:05:00.054]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.624]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.626]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.627]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.729]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.729]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.729]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.730]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.730]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.730]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.730]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.730]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"scheduler/manager.go:249","msg":"等待任务完成","超时时间：":60}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"3.4秒"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.432]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.433]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.433]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.433]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.434]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.435]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.435]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":60}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"7.0秒"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.579]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.582]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.582]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.582]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.583]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.086]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0853635,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.086]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.086]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0858766,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.086]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.091]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0909527,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.091]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.524]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.5246466,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.525]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":60}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"1分钟 29秒"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.567]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.568]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.568]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.569]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":60}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.450]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"0.9秒"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.450]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.088]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.089]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.089]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.089]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.091]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"2.4秒"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.121]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.123]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.123]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.123]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.130]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.132]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.132]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.132]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:40:30.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 14:40:30.016]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"auto-disbursement-compensation","attempt":0,"max_retries":3,"error":"查询待自动放款订单失败: 查询数据库失败: Error 1054 (42S22): Unknown column 'ba.availableProductID' in 'field list'"}
{"level":"dev.info","ts":"[2025-08-05 14:40:30.016]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"auto-disbursement-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.250]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.251]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.251]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.251]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.258]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.258]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.259]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.260]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.260]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.260]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.260]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.027]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0275122,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.027]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 14:41:00.040]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"auto-disbursement-compensation","attempt":0,"max_retries":3,"error":"查询待自动放款订单失败: 查询数据库失败: Error 1054 (42S22): Unknown column 'ba.availableProductID' in 'field list'"}
{"level":"dev.info","ts":"[2025-08-05 14:41:00.040]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"auto-disbursement-compensation","attempt":1,"retry_interval":30}
{"level":"dev.warn","ts":"[2025-08-05 14:41:10.001]","caller":"engine/scheduler.go:177","msg":"任务跳过执行（并发控制）","task_name":"auto-disbursement-compensation","concurrency_mode":"singleton","running_count":1}
{"level":"dev.warn","ts":"[2025-08-05 14:41:20.000]","caller":"engine/scheduler.go:177","msg":"任务跳过执行（并发控制）","task_name":"auto-disbursement-compensation","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.565]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.566]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.566]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.567]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.569]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:41:50.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-08-05 14:41:50.019]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"auto-disbursement-compensation","attempt":0,"max_retries":3,"error":"查询待自动放款订单失败: 查询数据库失败: Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'FROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tW' at line 10"}
{"level":"dev.info","ts":"[2025-08-05 14:41:50.020]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"auto-disbursement-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.665]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.667]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.667]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.667]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.670]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.671]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.671]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.671]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:42:20.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:42:20.015]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0148554,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:42:20.015]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:42:30.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:42:30.026]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0256449,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:42:30.026]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:42:40.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-05 14:42:40.014]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0128242,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-05 14:42:40.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
