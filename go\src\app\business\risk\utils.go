package risk

import (
	"fmt"
	"strconv"
	"time"

	"fincore/global"
	"fincore/model"
	"fincore/utils/shopspringutils"
)

// GetCustomerIDFromData 从数据中获取客户ID
func GetCustomerIDFromData(data map[string]interface{}) (uint64, error) {
	if customerIDVal, ok := data["customer_id"]; ok {
		switch v := customerIDVal.(type) {
		case string:
			if customerID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return customerID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case int64:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, fmt.Errorf("客户ID不能为空或格式错误")
}

// GetChannelIDFromData 从数据中获取渠道ID
func GetChannelIDFromData(data map[string]interface{}) (uint64, error) {
	if channelIDVal, ok := data["channel_id"]; ok {
		switch v := channelIDVal.(type) {
		case string:
			if channelID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return channelID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, nil
}

// findProductByID 根据产品ID查找匹配的产品
func findProductByID(products []*model.ProductRules, productID int) *model.ProductRules {
	for _, product := range products {
		if product.ID == productID {
			return product
		}
	}
	return nil
}

// isEvaluationValid 检查评估是否仍然有效
func isEvaluationValid(evaluationTime time.Time) bool {
	// 从配置中获取评估有效期（小时）
	expiryHours := global.App.Config.RiskEvaluation.EvaluationExpiryHours
	if expiryHours <= 0 {
		// 如果配置无效，默认24小时
		expiryHours = 24
	}

	// 计算过期时间
	expiryTime := evaluationTime.Add(time.Duration(expiryHours) * time.Hour)

	// 检查是否还在有效期内
	return time.Now().Before(expiryTime)
}

// QuotaCalculationResult 额度计算结果
type QuotaCalculationResult struct {
	NewAllQuota           float64 // 新的总额度
	CurrentBorrowedAmount float64 // 当前在借额度
	AvailableQuota        float64 // 可用额度
}

// calculateQuotaInfo 计算额度信息的通用函数（使用decimal进行精确计算）
func calculateQuotaInfo(accountAllQuota, accountReminderQuota, overallCreditLimit float64) QuotaCalculationResult {
	newAllQuota := overallCreditLimit
	currentBorrowedAmount := 0.0

	// 如果用户已有额度记录，需要计算在借额度
	if shopspringutils.CompareAmountsWithDecimal(accountAllQuota, 0) > 0 {
		// 使用decimal计算目前在借额度 = 账户总额度 - 账户可用额度
		currentBorrowedAmount = shopspringutils.SubtractAmountsWithDecimal(accountAllQuota, accountReminderQuota)
		// 确保在借额度不为负数
		if shopspringutils.CompareAmountsWithDecimal(currentBorrowedAmount, 0) < 0 {
			currentBorrowedAmount = 0
		}
		// 新总额度以系统计算总额度为标准，不超过系统限制
		if shopspringutils.CompareAmountsWithDecimal(accountAllQuota, overallCreditLimit) < 0 {
			newAllQuota = accountAllQuota
		} else {
			newAllQuota = overallCreditLimit
		}
	}

	// 使用decimal计算可用额度 = 新总额度 - 目前在借额度
	availableQuota := shopspringutils.SubtractAmountsWithDecimal(newAllQuota, currentBorrowedAmount)

	// 确保可用额度不小于0
	if shopspringutils.CompareAmountsWithDecimal(availableQuota, 0) < 0 {
		availableQuota = 0
	}

	// 使用CeilToTwoDecimal处理最终结果
	return QuotaCalculationResult{
		NewAllQuota:           shopspringutils.CeilToTwoDecimal(newAllQuota),
		CurrentBorrowedAmount: shopspringutils.CeilToTwoDecimal(currentBorrowedAmount),
		AvailableQuota:        shopspringutils.CeilToTwoDecimal(availableQuota),
	}
}
