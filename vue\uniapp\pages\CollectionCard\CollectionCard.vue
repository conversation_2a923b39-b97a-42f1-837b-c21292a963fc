<template>
	<view class="container">
		<!-- 表单区域 -->
		<view class="box">
			<view class="form-containera">
				<!-- 银行卡号输入 -->
				<view class="form-item">
					<text class="label">银行卡号:</text>
					<input v-model="formData.bankNumber" type="number" placeholder="请输入银行卡号" maxlength="19" class="right-align-input" />
				</view>

				<!-- 预留手机号 -->
				<view class="form-item">
					<text class="label">预留手机号:</text>
					<input v-model="formData.mobile" type="number" placeholder="请输入手机号" maxlength="11" class="right-align-input" />
				</view>

				<!-- 验证码 -->
				<view class="form-itema">
					<text class="labela">验证码:</text>
					<input v-model="formData.smsCode" type="number" maxlength="6" class="right-align-input" />
					<div class="sms-btn" :disabled="isSending" @click="sendSmsCode">
						{{ isSending ? `${countdown}s后重试` : "发送验证码" }}
					</div>
				</view>
			</view>
		</view>

		<!-- 银行不支持提示 -->
		<view class="bank-notice">
			<text>
				<image src="/static/icon/jg.png" mode=""></image>中国民生银行不支持当前服务
			</text>
		</view>

		

		
		<view class="footer-box">
			<!-- 协议勾选 -->
			<view class="agreement">
				<checkbox-group @change="toggleAgreement" class="agreement-group">
					<checkbox :checked="isAgreed" color="#1A73E8" style="transform: scale(0.7)" />
					<text class="agreement-text">
						我已阅读并同意<text class="link" @click="linka">《扣款通知函》</text>
					</text>
				</checkbox-group>
			</view>
			<!-- 提交按钮 -->
			<button class="submit-btn" :class="!canSubmit?'disabled':''" @click="handleSubmit">
				提交
			</button>
		</view>
		
	</view>
</template>

<script>
	import bankApi from "@/api/bank";
	import BIN from 'bankcardinfo';
	import cardApi from '@/api/card.js';
	export default {
		data() {
			return {
				formData: {
					bankNumber: "",
					mobile: "",
					smsCode: ""
				},
				isSending: false,
				countdown: 60,
				isAgreed: false,
				smsData: {},
				isSubmit: false, // 是否可以提交
				order_no: ""
			};
		},
		computed: {
			canSubmit() {
				const pureCardNo = this.formData.bankNumber.replace(/\s/g, "");
				return (
					/^\d{16,19}$/.test(pureCardNo) &&
					/^1[3-9]\d{9}$/.test(this.formData.mobile) &&
					/^\d{6}$/.test(this.formData.smsCode) &&
					this.isAgreed &&
					this.isSubmit
				);
			},
		},
		onLoad() {
		},
		methods: {
			// 根据卡号获取银行相关信息
			async getBankBinInfo(cardId) {
				return new Promise((resove,reject) => {
					BIN.getBankBin(cardId, (err,data) => {
						if(!err) {
							resove(data);
						}else{
							reject(err);
						}
					})
				})
			},
			// 格式化银行卡号显示
			formatCardNumber() {
				this.formData.bankNumber = this.formData.bankNumber
					.replace(/\D/g, "")
					.replace(/(\d{4})(?=\d)/g, "$1 ");
			},
			// 银行卡校验
			luhnCheck(cardNo) {
				let sum = 0;
				let alternate = false;
				for (let i = cardNo.length - 1; i >= 0; i--) {
					let digit = parseInt(cardNo.charAt(i));
					if (alternate) {
						digit *= 2;
						if (digit > 9) digit -= 9;
					}
					sum += digit;
					alternate = !alternate;
				}
				return sum % 10 === 0;
			},
			validateBankCard(cardNo) {
				// 移除空格
				cardNo = cardNo.replace(/\s/g, '');

				// 基础格式校验
				if (!/^\d{16,19}$/.test(cardNo)) return false;

				// 银联卡额外校验
				if (/^62/.test(cardNo) && !/^62\d{14,17}$/.test(cardNo)) {
					return false;
				}

				// Luhn算法校验
				return this.luhnCheck(cardNo);
			},
			// ****************
			// 发送验证码
			async sendSmsCode() {
				const pureCardNo = this.formData.bankNumber.replace(/\s/g, "");
				// if (!/^\d{16,19}$/.test(pureCardNo)) {
				if (!this.validateBankCard(pureCardNo)) {
					return uni.showToast({
						title: "请输入正确的银行卡号",
						icon: "none",
					});
				}
				
				if (!/^1[3-9]\d{9}$/.test(this.formData.mobile)) {
					return uni.showToast({
						title: "请输入正确的手机号",
						icon: "none",
					});
				}
				try{
					let cardData = await this.getBankBinInfo(pureCardNo);
					// console.log(cardData)
					let params = {
						...cardData,
						banknumber: pureCardNo,
						mobile: this.formData.mobile
					}
					uni.showLoading({
						title: "发送中..."
					});
					console.log(params)
					cardApi.postBindBankCardSms(params).then(res => {
						// console.log(res)
						if(res.code == 0 && res.data.need_sms) {
							this.isSending = true;
							// 开始倒计时
							this.countdown = 60;
							const timer = setInterval(() => {
								this.countdown--;
								if (this.countdown <= 0) {
									clearInterval(timer);
									this.isSending = false;
								}
							}, 1000);
							uni.showToast({
								title: "验证码已发送",
								icon: "none",
							});
							this.order_no = res.data.third_party_order_no;
							this.isSubmit = true;
							
						}
						if(!res.data.need_sms) {
							uni.showToast({
								title: "此银行卡已绑定",
								icon: "none"
							})
						}
					});
					
				}catch (error) {
					this.isSending = false;
					uni.showToast({
						// title: error.message || "验证码发送失败",
						title: "验证码发送失败",
						icon: "none",
					});
				}
				
			},
			// 提交绑卡
			async handleSubmit() {
				if(!this.canSubmit) {
					return false
				}
				try {
					const params = {
						mobile: this.formData.mobile,
						banknumber: this.formData.bankNumber.replace(/\s/g, ""),
						code: this.formData.smsCode, // 添加验证码参数
						thirdPartyOrderNo: this.order_no
					};

					const res = await cardApi.postBindBankCard(params);
					if(res.code == 0) {
						uni.showToast({
							title: "绑卡成功",
							icon: "success",
							success: () => {
								// setTimeout(() => {
								// 	uni.navigateTo({
								// 		url: '/pages/BorrowMoney/BorrowMoney'
								// 	});
								// }, 1500);
								setTimeout(() => {
									// #ifdef WEB
									window.history.back();
									// #endif
									// #ifndef WEB
									uni.navigateBack();
									// #endif
								},1500)
							},
						});
					}
					
				} catch (error) {
					uni.showToast({
						title: error.message || "绑卡失败",
						icon: "none",
					});
				}
			},
			linka() {
				uni.navigateTo({
					url: "/pages/RepaymentCard/hlink",
				});
			},
			// 协议勾选
			toggleAgreement(e) {
				this.isAgreed = e.detail.value.length > 0;
			},

		},
	};
</script>

<style lang="scss">
	.container {
		min-height: 100%;
		background-color: #eff2f7;
		overflow: hidden;
	}
	.box{
		padding: 20rpx;
	}
	.form-containera {
		padding: 0 20rpx;
		border-radius: 15rpx;
		background-color: #fff;
	}

	.form-item {
		padding: 20rpx;
		border-bottom: 1px solid #f4f4f4;
		display: flex;
		align-items: center;
		color: #333;
		.label {
			font-size: 28rpx;
		}

		input {
			height: 60rpx;
			margin-left: 25rpx;
			font-size: 28rpx;
			text-align: right;
			flex: 1;
		}
	}

	.form-itema {
		padding: 20rpx;
		display: flex;
		align-items: center;
		.labela {
			font-size: 28rpx;
		}

		input {
			font-size: 28rpx;
			flex: 1;
			height: 60rpx;
			margin-left: 25rpx;
			font-size: 28rpx;
			text-align: right;
		}

		.sms-btn {
			margin-left: 25rpx;
			font-weight: 600;
			color: #2289ff;
			font-size: 28rpx;
		}
	}

	// 新增样式：右对齐输入框
	.right-align-input {
		text-align: right;
	}

	.bank-notice {
		width: 100%;
		min-height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10px;
		font-size: 15px;
		color: #222;
		font-size: 28rpx;

		text {
			display: flex;
			align-items: center;
		}

		image {
			width: 38rpx;
			height: 38rpx;
			margin-right: 6px;
			vertical-align: middle;
			position: relative;
			top: -2px;
		}
	}

	

	
	.footer-box{
		position: fixed;
		bottom: 60rpx;
		left: 30rpx;
		right: 30rpx;
	}
	.agreement {
		height: 20px;
		font-size: 24rpx;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: center;
		.agreement-group{
			display: flex;
			align-items: center;
		}
		checkbox {
			vertical-align: middle;
		}
		.agreement-text {
			vertical-align: middle;
		}
	}
	.submit-btn {
		background-color: #007aff;
		border-radius: 50rpx;
		text-align: center;
		color: #fff;
		&.disabled{
			background-color: #ddd;
		}
	}
	
</style>