package model

import (
	"fincore/utils/gf"
	"fincore/utils/pagination"
	"fmt"
	"time"
)

// ChannelStatistics 渠道统计数据模型
type ChannelStatistics struct {
	ID                   uint       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	ChannelID            uint       `json:"channel_id" db:"channel_id"`
	NewCustomerRegNum    uint       `json:"new_customer_reg_num" db:"new_customer_reg_num"`
	RealNameNum          uint       `json:"real_name_num" db:"real_name_num"`
	NumberOfTransactions uint       `json:"number_of_transactions" db:"number_of_transactions"`
	CreatedAt            time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt            *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
}

// NewChannelStatistics 创建渠道统计实例
func NewChannelStatistics() *ChannelStatistics {
	return &ChannelStatistics{}
}

// TableName 返回表名
func (cs *ChannelStatistics) TableName() string {
	return "channel_statistics"
}

// ChannelStatisticsService 渠道统计服务
type ChannelStatisticsService struct{}

// NewChannelStatisticsService 创建渠道统计服务实例
func NewChannelStatisticsService() *ChannelStatisticsService {
	return &ChannelStatisticsService{}
}

// GetChannelStatisticsList 获取渠道统计列表
func (s *ChannelStatisticsService) GetChannelStatisticsList(params map[string]interface{}) (map[string]interface{}, error) {
	// 构建查询条件
	query := DB().Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Fields("cs.*, c.channel_name, c.channel_code")

	// 添加查询条件
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		query = query.Where("cs.channel_id", channelID)
	}

	if startDate, ok := params["start_date"]; ok && startDate != "" {
		query = query.Where("DATE(cs.created_at)", ">=", startDate)
	}

	if endDate, ok := params["end_date"]; ok && endDate != "" {
		query = query.Where("DATE(cs.created_at)", "<=", endDate)
	}

	// 排序
	query = query.OrderBy("cs.id DESC")

	// 分页查询
	page := gf.InterfaceToInt(params["page"])
	pageSize := gf.InterfaceToInt(params["page_size"])
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 获取总数
	countQuery := DB().Table("channel_statistics cs")
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		countQuery = countQuery.Where("cs.channel_id", channelID)
	}
	if startDate, ok := params["start_date"]; ok && startDate != "" {
		countQuery = countQuery.Where("DATE(cs.created_at)", ">=", startDate)
	}
	if endDate, ok := params["end_date"]; ok && endDate != "" {
		countQuery = countQuery.Where("DATE(cs.created_at)", "<=", endDate)
	}

	total, err := countQuery.Count()
	if err != nil {
		return nil, fmt.Errorf("获取统计总数失败: %v", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	result, err := query.Limit(pageSize).Offset(offset).Get()
	if err != nil {
		return nil, fmt.Errorf("获取渠道统计列表失败: %v", err)
	}

	// 构建分页信息
	paginationInfo := pagination.BuildPaginationInfo(total, page, pageSize)

	return map[string]interface{}{
		"list":       result,
		"pagination": paginationInfo,
	}, nil
}

// GetChannelStatisticsDetail 获取渠道统计详情
func (s *ChannelStatisticsService) GetChannelStatisticsDetail(id uint) (map[string]interface{}, error) {
	result, err := DB().Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Fields("cs.*, c.channel_name, c.channel_code").
		Where("cs.id", id).
		First()

	if err != nil {
		return nil, fmt.Errorf("获取渠道统计详情失败: %v", err)
	}

	if result == nil {
		return nil, fmt.Errorf("渠道统计记录不存在")
	}

	return result, nil
}

// CreateChannelStatistics 创建渠道统计记录
func (s *ChannelStatisticsService) CreateChannelStatistics(data map[string]interface{}) error {
	// 设置创建时间
	data["created_at"] = time.Now()

	_, err := DB().Table("channel_statistics").Insert(data)
	if err != nil {
		return fmt.Errorf("创建渠道统计记录失败: %v", err)
	}

	return nil
}

// UpdateChannelStatistics 更新渠道统计记录
func (s *ChannelStatisticsService) UpdateChannelStatistics(id uint, data map[string]interface{}) error {
	// 移除不允许更新的字段
	delete(data, "id")
	delete(data, "created_at")

	_, err := DB().Table("channel_statistics").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新渠道统计记录失败: %v", err)
	}

	return nil
}

// DeleteChannelStatistics 软删除渠道统计记录
func (s *ChannelStatisticsService) DeleteChannelStatistics(id uint) error {
	data := map[string]interface{}{
		"deleted_at": time.Now(),
	}

	_, err := DB().Table("channel_statistics").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("删除渠道统计记录失败: %v", err)
	}

	return nil
}

// GetChannelStatisticsByDate 根据日期获取渠道统计
func (s *ChannelStatisticsService) GetChannelStatisticsByDate(channelID uint, date string) (map[string]interface{}, error) {
	result, err := DB().Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Fields("cs.*, c.channel_name, c.channel_code").
		Where("cs.channel_id", channelID).
		Where("DATE(cs.created_at)", date).
		Where("cs.deleted_at IS NULL").
		First()

	if err != nil {
		return nil, fmt.Errorf("获取渠道统计失败: %v", err)
	}

	return result, nil
}

// GetChannelStatisticsSummary 获取渠道统计汇总数据
func (s *ChannelStatisticsService) GetChannelStatisticsSummary(params map[string]interface{}) (map[string]interface{}, error) {
	query := DB().Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Fields(`
			COUNT(*) as total_records,
			SUM(cs.new_customer_reg_num) as total_new_customers,
			SUM(cs.real_name_num) as total_real_name,
			SUM(cs.number_of_transactions) as total_transactions
		`).
		Where("cs.deleted_at IS NULL")

	// 添加查询条件
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		query = query.Where("cs.channel_id", channelID)
	}

	if startDate, ok := params["start_date"]; ok && startDate != "" {
		query = query.Where("DATE(cs.created_at)", ">=", startDate)
	}

	if endDate, ok := params["end_date"]; ok && endDate != "" {
		query = query.Where("DATE(cs.created_at)", "<=", endDate)
	}

	result, err := query.First()
	if err != nil {
		return nil, fmt.Errorf("获取渠道统计汇总失败: %v", err)
	}

	return result, nil
}
