{"level":"dev.info","ts":"[2025-08-05 09:10:35.512]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.517]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:10:35.531]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.454]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.456]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:11:44.463]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.273]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.277]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:25:59.293]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.469]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.472]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:26:42.490]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.377]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.381]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:28:02.394]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.688]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.701]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:28:50.712]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.117]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.122]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:29:29.136]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.929]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.937]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:29:55.965]","caller":"bootstrap/router.go:31","msg":"数据库连接实例错误: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: YES)"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.742]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.746]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:33:23.778]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.661]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.664]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:36:30.707]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.703]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.709]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:37:11.756]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.493]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 创建还款支付失败","data":"\"资管费已全部支付\""}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.670]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.672]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:43:26.718]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.021]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.026]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:44:47.062]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.891]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.895]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:45:55.962]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.183]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.187]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 09:47:16.218]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 09:48:10.910]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 查询支付状态失败","data":"\"第三方查询失败: tant004 - 签名验证失败[EG000001]\""}
{"level":"dev.info","ts":"[2025-08-05 09:58:41.783]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 查询支付状态失败","data":"\"第三方查询失败: tant004 - 签名验证失败[EG000001]\""}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.852]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.854]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 10:24:12.873]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.577]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.579]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 10:24:41.588]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.494]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.498]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:17:17.534]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.225]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.228]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:20:12.287]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.124]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.126]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:22:22.156]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.444]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.448]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:26:34.478]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.011]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.015]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:29:17.055]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.852]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.855]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:32:11.886]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.139]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.144]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:36:48.203]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.115]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.118]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:39:18.151]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.930]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:40:21.932]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:40:22.043]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.417]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.420]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:42:51.470]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.331]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.334]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:44:17.418]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.482]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.491]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:45:30.550]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.257]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.260]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:47:45.271]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.568]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.571]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:51:46.630]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.303]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.306]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:54:46.386]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.265]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.267]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 11:55:19.309]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.008]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.010]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 12:02:04.044]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.628]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.632]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:08:05.664]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.731]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.734]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.765]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:09:36.771]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.081]","caller":"src/main.go:53","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"src/main.go:53","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"src/main.go:53","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-05 14:09:40.082]","caller":"src/main.go:53","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.436]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.439]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.478]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:10:48.481]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.396]","caller":"src/main.go:53","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"src/main.go:53","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.397]","caller":"src/main.go:53","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-05 14:10:55.398]","caller":"src/main.go:53","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.584]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.586]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.615]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:13:11.621]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"src/main.go:53","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"src/main.go:53","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.477]","caller":"src/main.go:53","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-05 14:14:41.478]","caller":"src/main.go:53","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.571]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.573]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.640]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:15:23.680]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.449]","caller":"src/main.go:53","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.450]","caller":"src/main.go:53","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.450]","caller":"src/main.go:53","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-05 14:15:24.450]","caller":"src/main.go:53","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.092]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.094]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.103]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:17:10.104]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.474]","caller":"src/main.go:53","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"src/main.go:53","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.475]","caller":"src/main.go:53","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-05 14:17:12.476]","caller":"src/main.go:53","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.132]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.136]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.169]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:40:25.180]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.260]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.262]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.290]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:40:53.297]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.570]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.574]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.607]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:41:49.614]","caller":"src/main.go:53","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.672]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.678]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.732]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-05 14:42:15.744]","caller":"src/main.go:53","msg":"启动端口：8108"}
