<template>
	<view class="newbox">
		<view class="page-container">
			<div :style="{ height: statusBarHeight + 'px' }"></div>
			<!-- 顶部用户信息栏 -->
			<view class="user-header">
				<text class="app-name">{{ userInfo.name || '悦心花' }}</text>
				<text class="user-phone">
					{{ getShowMobile() }}
				</text>
				<view class="action-buttons">
					<button class="service-btn" @click="handleService">
						<image src="/static/icon/kf1.png" class="btn-icon" />
						<text>客服</text>
					</button>
					<button class="complaint-btn" @click="handleComplaint">
						<image src="/static/icon/ts.png" class="btn-icon" />
						<text>投诉</text>
					</button>
				</view>
			</view>

			<!-- 安全提示 -->
			<view class="security-alert">
				<template v-if="hasReply">
					<view class="security-resp">
						<image src="/static/icon/tshf.png" mode="widthFix"></image>
						<text style="color: #f56c6c; font-weight: bold; cursor: pointer;" @click="goToReply">
							已回复投诉，请点击查看
						</text>
					</view>
				</template>
				<template v-else>
					<view class="security-warn">
						<image src="/static/icon/ts2.png" mode="widthFix"></image>
						<text>除APP内企业微信客服外，其他主动添加您的都是骗子！</text>
					</view>
				</template>
			</view>
		
			<!-- 额度展示区 -->
			<view class="home-card">
				<view class="home-card-box">
					<view class="home-card-top">
						<view class="home-card-top-item">
							<image src="/static/image/index/card1.png" mode="widthFix"></image>
							<text>额度高</text>
						</view>
						<view class="home-card-top-item">
							<image src="/static/image/index/card2.png" mode="widthFix"></image>
							<text>放款快</text>
						</view>
						<view class="home-card-top-item">
							<image src="/static/image/index/card3.png" mode="widthFix"></image>
							<text>正规持牌</text>
						</view>
					</view>
					
					<view class="home-card-content">
						<template v-if="riskResult == -1 || riskResult == 0">
							<view class="home-card-title">
								{{ riskResult == 0?'可用额度(元)':'可借额度(元)' }}
							</view>
							<view class="home-card-price">
								{{ riskResult == 0?availableCreditLimit:amountLimit}}
							</view>
						</template>
						<template v-else-if="riskResult == 1">
							<view class="home-card-title">
								审核中
							</view>
							<view class="home-card-price">
								认证审核中,请耐心等待
							</view>
						</template>
						<template v-else>
							<view class="home-card-title">
								综合评分不足
							</view>
							<view class="home-card-price">
								很遗憾审核未通过
							</view>
						</template>
						<view class="home-card-rote">
							年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）
						</view>
					</view>
					<view class="home-card-apply" v-if="riskResult == -1 || riskResult == 0">
						<button @click="toGetMoney">马上拿钱</button>
					</view>
				</view>
			</view>
			<!-- 白色背景流程步骤块 -->
			<view class="white-step">
				<view class="white-step-item">
					<image src="/static/image/index/white1.png" mode="heightFix"></image>
					<text>身份认证</text>
				</view>
				<image class="white-step-next" src="/static/image/index/white4.png"/>
				<view class="white-step-item">
					<image src="/static/image/index/white2.png" mode="heightFix"></image>
					<text>绑定银行卡</text>
				</view>
				<image class="white-step-next" src="/static/image/index/white4.png"/>
				<view class="white-step-item">
					<image src="/static/image/index/white3.png" mode="heightFix"></image>
					<text>持牌机构放款</text>
				</view>
			</view>
			<!-- 产品介绍 -->
			<view class="product">
				<view class="product-title">
					产品介绍
				</view>
				<view class="product-list">
					<view class="product-item bg1">
						<view class="product-item-name">
							安全保障
						</view>
						<view class="product-item-desc">
							信息加密 放款安全
						</view>
					</view>
					<view class="product-item bg2">
						<view class="product-item-name">
							快速审核
						</view>
						<view class="product-item-desc">
							线上申请 极速放款
						</view>
					</view>
					<view class="product-item bg3">
						<view class="product-item-name">
							机构合作
						</view>
						<view class="product-item-desc">
							多家权威机构合作
						</view>
					</view>
					<view class="product-item bg4">
						<view class="product-item-name">
							额度灵活
						</view>
						<view class="product-item-desc">
							定期评估调整额度
						</view>
					</view>
				</view>
			</view>
			
		</view>
		<!-- 在线客服底部弹框 -->
		<div class="service-modal" v-if="showServiceModal" @click="closeServiceModal">
			<div class="modal-content" @click.stop>
				<div class="modal-header">
					<text class="modal-title" @click="handleCall">电话客服: ************</text>
					<div class="modal-close" @click="closeServiceModal">
						取消
					</div>
				</div>
			</div>
		</div>



		<!-- 添加到桌面 -->
		<uni-popup ref="refdesktop" :mask-click="false">
			<div class="add-to-home-screen">
				<div class="prompt-container">
					<div class="prompt-header">
						<h3>添加到主屏幕</h3>
					</div>

					<div class="prompt-content">
						<!-- <div class="icon-container">
							<div class="share-icon">📲</div>
							<div class="arrow-icon">➡️</div>
							<div class="home-icon">🏠</div>
						</div> -->
						<div class="prompt-steps" v-if="isIOS">
							<view class="step-item">
								<view class="step-title">
									<text class="step-text-gl">第一步：</text>点击屏幕下方该按钮(推荐使用<text class="step-text-gl">safari浏览器</text>)
								</view>
								<image src="/static/image/daskStep1.png" mode="widthFix"></image>
								<view class="step-title">
									<text class="step-text-gl">第二步：</text>在弹出框中选择<text class="step-text-gl">“添加到主屏幕”</text>
								</view>
								<image src="/static/image/daskStep2.png" mode="widthFix"></image>
							</view>
						</div>
						<div class="prompt-steps" v-else>
							<view class="step-item">
								<view class="step-title">
									<view><text>第一步：</text>点击浏览器菜单</view>
								</view>
								<view class="step-title">
									<view><text>第二步：</text>在弹出框中选择<text>添加到主屏幕</text>或<text>添加到桌面</text></view>
								</view>
							</view>
						</div>
					</div>

				</div>
			</div>
		</uni-popup>




	</view>
</template>
<script setup>
	import {
		ref
	} from "vue";
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";
	import utils from "@/utils";
	import user from '@/store/user.js';
	import {
		getShowMobile as getShowMobileHelper
	} from '@/utils/helper';
	import complaintApi from '@/api/complaint.js';
	import userApi from '@/api/user.js';
	import {
		storeToRefs
	} from 'pinia';
	const userStore = user();
	const List = ref([]);
	const showServiceModal = ref(false);
	
	const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
	// userInfo -- 用户信息
	// riskResult -- 是否审核通过 0 通过 1 审核 2 拒绝 3 调用风控模型失败
	// amountLimit 额度
	const {
		userInfo,
		riskResult,
		amountLimit,
		availableCreditLimit,
		dayRate,
		cid
	} = storeToRefs(userStore);
	const hasReply = ref(false);


	const refdesktop = ref();
	
	// #ifndef APP
	const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
	const isAndroid = /android/i.test(navigator.userAgent);
	function isMobile() {
		return isIOS || isAndroid;
	}
	
	function isStandalone() {
		return ('standalone' in window.navigator) && window.navigator.standalone;
	}
	// #endif
	

	onLoad(async () => {
		// 获取首页数据
		
		// #ifndef APP
		console.log(isIOS)
		// if (isMobile() && !isStandalone()) {
		if (isIOS && !isStandalone()) {
			setTimeout(() => {
				refdesktop.value.open();
				uni.hideTabBar();
			}, 500)
		}else{
			setTimeout(() => {
				refdesktop.value.close();
				uni.showTabBar();
			}, 500)
		}
		// #endif
		

	});
	onShow(async () => {
		await userStore.getInfo();
		let uid = userInfo.value?.uid;
		if (!uid) {
			uid = userInfo.value?.uid;
		}
		if (!uid) {
			hasReply.value = false;
			return;
		}
		if (userInfo.value.identityStatus && userInfo.value.identityStatus == 2) { // 实名认证审核通过后再获取
			// 更新贷款是否审核通过状态
			await userStore.getEvaluate(uid);
			// 获取授信额度&贷款产品
			await userStore.getProducts(uid);
		}


		// 获取投诉回复
		try {
			const res = await complaintApi.getComplaintRes(Number(uid));
			if (res && res.code === 0 && res.data && res.data.complainResponse) {
				hasReply.value = true;
			} else {
				hasReply.value = false;
			}
		} catch (e) {
			hasReply.value = false;
		}
	});

	const toPage = () => {};

	// 登录校验方法
	function checkLogin() {
		if (!userStore.isLogin) {
			uni.reLaunch({
				url: '/pages/login/login'
			});
			return false;
		}
		return true;
	}

	// 顶部客服按钮
	function handleService() {
		if (!checkLogin()) return;
		showServiceModal.value = true;
	}

	function closeServiceModal() {
		showServiceModal.value = false;
	}

	function handleCall() {
		uni.makePhoneCall({
			phoneNumber: '************'
		})
	}
	// 顶部投诉按钮
	const handleComplaint = async () => {
		if (!checkLogin()) return;

		userStore.nextCallBack().then(res => {
			// 这里写跳转投诉页面逻辑
			uni.navigateTo({
				url: '/pages/index/complaint'
			});
		}).catch(err => {
			if (err.code == 2) {
				uni.showToast({
					title: "请先完成实名认证",
					icon: "none"
				})
			}
			if (err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});

	}

	// 点击马上拿钱跳转到实名认证页面
	const toGetMoney = async () => {
		if (!checkLogin()) return;
		userStore.nextCallBack().then(res => {
			uni.navigateTo({
				url: "/pages/BorrowMoney/BorrowMoney",
			});
		}).catch(err => {
			if (err.url) {
				uni.navigateTo({
					url: err.url
				});
			}
			if (err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});
	};

	function getShowMobile() {
		return getShowMobileHelper(userStore);
	}

	function goToReply() {
		uni.navigateTo({
			url: '/pages/index/Reply'
		});
	}
</script>

<style lang="less" scoped>
	page {}

	.newbox {
		min-height: 100vh;
		// background: linear-gradient(180deg, #eff2f9, #ffffff);
		background: linear-gradient(180deg, #e6ecf7, #fff);

		.listbox {
			.Item {
				display: flex;
				margin-bottom: 10px;
				padding: 0px 10px;
				border-bottom: #e5e6eb solid 1px;

				.mright {
					flex: 1;
					padding: 0px 10px;
					width: 100%;

					.toptitle {
						display: flex;

						.uname {
							flex: 1;
							font-size: 18px;
							font-weight: 600;
						}

						.time {
							font-size: 12px;
							color: #999;
						}
					}

					.des {
						width: 90%;
						margin-top: 5px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}

	.page-container {
		padding-bottom: 100rpx;
	}

	.user-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 60rpx 30rpx 20rpx;

		.app-name {
			font-size: 26px;
			font-weight: 900;
			color: #333;
			font-family: 'Microsoft YaHei', sans-serif;
		}

		.user-phone {
			font-size: 28rpx;
			color: #666;
			margin-left: -100px;
		}

		.action-buttons {
			display: flex;

			button {
				background: transparent;
				border: none;
				padding: 0;
				margin: 0 15rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				&::after {
					border: none;
				}

				text {
					font-size: 20rpx;
					color: #888;
					line-height: 30rpx;
				}
			}

			.btn-icon {
				width: 36rpx;
				height: 36rpx;
			}
		}
	}

	.security-alert {
		// background: #fff8e6;
		padding: 16rpx 30rpx;
		font-size: 24rpx;
		color: #f56c6c;

		.security-resp {
			display: flex;
			align-items: center;

			image {
				width: 35rpx;
				margin-right: 10rpx;
			}
		}

		.security-warn {
			display: flex;
			align-items: center;

			image {
				width: 35rpx;
				margin-right: 10rpx;
			}
		}
	}

	.quota-section {
		background: linear-gradient(135deg, #4a8cff, #1a6eff);
		margin: 20rpx 30rpx 0;
		padding: 40rpx;
		border-radius: 16rpx;
		color: white;
		text-align: center;
		position: relative;

		.quota-title {
			display: block;
			color: #fff;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
			font-size: 28rpx;
			background: #4c91db;
			border-radius: 15rpx 0 15rpx 0;
			padding: 10rpx 25rpx;
		}

		.quota-amount {
			font-size: 40rpx;
			font-weight: bold;
			display: block;
			margin: 50rpx 0 20rpx;

			.quota-amount-price {
				margin-left: 10rpx;
				vertical-align: middle;
			}
		}

		.apply-btn {
			background: white;
			color: #1a73e8;
			border-radius: 50rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 32rpx;
			font-weight: bold;
			margin: 0 auto;
			width: 80%;
		}

		.rate-info {
			font-size: 24rpx;
			display: block;
			margin-top: 20rpx;
			opacity: 0.8;
		}
	}

	.container {
		height: 100%;
		padding: 20rpx;
	}

	.card-container {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		flex-wrap: wrap;
	}

	.feature-card {
		width: 210rpx;
		height: 220rpx;
		background: linear-gradient(180deg, #f6faff 80%, #eaf3ff 100%);
		border-radius: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(26, 110, 255, 0.08);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		padding: 24rpx 0 0 0;
		margin-bottom: 0;
		transition: all 0.3s;
		border: 1rpx solid #e3eefd;

		&:active {
			transform: scale(0.98);
			box-shadow: 0 2rpx 8rpx rgba(26, 110, 255, 0.12);
		}
	}

	.icon-wrapper {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fff;
		border-radius: 50%;
		box-shadow: 0 2rpx 8rpx rgba(26, 110, 255, 0.10);

		.icon {
			width: 60rpx;
			height: 60rpx;
		}
	}

	.text-wrapper {
		text-align: center;

		.title {
			font-size: 28rpx;
			font-weight: bold;
			color: #222;
			display: block;
			margin-bottom: 6rpx;
			letter-spacing: 1rpx;
		}

		.desc {
			font-size: 20rpx;
			color: #8ba3c7;
			line-height: 1.2;
			letter-spacing: 0.5rpx;
		}
	}

	.step-blocks {
		font-size: 10px;
		text-align: center;
		padding: 0 30rpx;
	}

	.step-blocks image {
		width: 100%;
	}

	/* 客服弹框样式 */
	.service-modal {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: flex-end;
		align-items: flex-end;
		z-index: 999;
	}

	.modal-content {
		width: 100%;
		height: 110px;
		background-color: #fff;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		overflow: hidden;
		position: relative;
		box-shadow: none;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 24px;
	}

	.modal-header {
		width: 100%;
		text-align: center;
		padding: 0;
		border-bottom: none;
	}

	.modal-title {
		display: block;
		font-size: 16px;
		font-weight: normal;
		color: #333;
		margin-bottom: 24px;
		margin-top: 0;
		line-height: 1.5;
	}

	.modal-close {
		margin-top: 10px;
		font-size: 14px;
		color: #bbb;
		cursor: pointer;
		text-align: center;
		font-weight: normal;
	}

	.audit-pass-hint {
		display: flex;
		flex-direction: column;
		padding: 50rpx 30rpx 30rpx;

		.pass-hint-t {
			font-size: 30rpx;

		}

		.pass-hint-tips {
			margin-top: 25rpx;
			font-size: 40rpx;
		}
	}



	/* 提示框样式 */
	.add-to-home-screen {
		width: 90vw;
		color: white;
	}

	.prompt-container {
		// background: linear-gradient(135deg, #6e8efb, #a777e3);
		background: linear-gradient(to bottom, #1a6eff, #1b5bc5);
		border-radius: 20rpx;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
		margin: 0 auto;
		height: 100%;
		padding: 30rpx;
	}

	.prompt-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}

	.prompt-header h3 {
		margin: 0;
		font-size: 1.3rem;
		font-weight: 600;
	}

	.close-btn {
		background: none;
		border: none;
		color: white;
		font-size: 1.5rem;
		cursor: pointer;
		padding: 0 10px;
	}

	.prompt-content {
		text-align: center;
		margin-bottom: 20px;
	}

	.icon-container {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 15px;
	}

	.share-icon,
	.home-icon {
		font-size: 2rem;
		margin: 0 10px;
	}

	.arrow-icon {
		font-size: 1.5rem;
		margin: 0 5px;
	}

	.highlight {
		font-weight: bold;
		color: #ffeb3b;
	}

	.prompt-footer {
		display: flex;
		justify-content: space-between;
	}

	.dismiss-btn,
	.never-btn {
		flex: 1;
		padding: 10px;
		border: none;
		border-radius: 8px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
	}

	.dismiss-btn {
		background-color: rgba(255, 255, 255, 0.2);
		color: white;
		margin-right: 10px;
	}

	.dismiss-btn:hover {
		background-color: rgba(255, 255, 255, 0.3);
	}

	.never-btn {
		background-color: rgba(255, 255, 255, 0.8);
		color: #6e8efb;
	}

	.never-btn:hover {
		background-color: white;
	}
	
	.prompt-steps{
		background: #fff;
		color: #333;
		border-radius: 15rpx;
		padding: 20rpx;
		.step-title{
			font-size: 24rpx;
			margin-top: 30rpx;
			margin-bottom: 10rpx;
			text-align: left;
			font-weight: bold;
			.step-text-gl{
				color: #1a6eff;
			}
		}
		.step-item{
			padding: 0 20rpx;
			image{
				width: 70%;
			}
		}
	}
	
	
	
	//  新ui样式
	// 卡片区
	.home-card{
		margin-top: 10rpx;
		padding: 0 30rpx;
		.home-card-box{
			height: 360rpx;
			background: url("/static/image/index/card-bg.png") no-repeat;
			background-size: 100% 100%;
			display: flex;
			flex-direction: column;
			.home-card-top{
				display: flex;
				align-items: center;
				justify-content: space-around;
				height: 62rpx;
				.home-card-top-item{
					display: flex;
					align-items: center;
					color: #fff;
					image{
						width: 30rpx;
						max-height: 30rpx;
					}
					text{
						font-size: 20rpx;
						margin-left: 10rpx;
					}
				}
			}
			
			.home-card-content{
				flex: 1;
				display: flex;
				flex-direction: column;
				color: #fff;
				justify-content: center;
				align-items: center;
				font-size: 34rpx;
				.home-card-title{
				}
				.home-card-price{
					margin: 10rpx 0;
				}
				.home-card-rote{
					font-size: 20rpx;
				}
			}
			.home-card-apply{
				padding: 0 100rpx 30rpx;
				button{
					height: 80rpx;
					line-height: 80rpx;
					color: #0d5ccb;
					font-size: ;
					background-color: #fff;
					font-size: 34rpx;
					outline: none;
					border-radius: 50rpx;
					font-weight: bold;
				}
			}
			
			
		}
	}
	
	// 白色背景流程块样式
	.white-step{
		margin: 0 30rpx;
		background: #fff;
		display: flex;
		overflow: hidden;
		.white-step-next{
			width: 13rpx;
			height: 19rpx;
			margin-top: 26rpx;
		}
		.white-step-item{
			flex: 1;
			height: 108rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			image{
				height: 45rpx;
				max-width: 52rpx;
			}
			text{
				font-size: 20rpx;
				color: #000;
				margin-top: 12rpx;
			}
		}
	}
	
	// 产品介绍板块
	.product{
		padding: 0 30rpx;
		.product-title{
			padding: 40rpx 0 25rpx;
			font-size: 30rpx;
			color: #000;
			font-weight: bold;
		}
		.product-list{
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			.product-item{
				width: 335rpx;
				height: 155rpx;
				background: #e8f6fe;
				border-radius: 10rpx;
				background-size: 100% 100%;
				margin-bottom: 15rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				justify-content: center;
				.product-item-name{
					color: #4c6db6;
					font-size: 26rpx;
					font-weight: bold;
				}
				.product-item-desc{
					color: #8791b5;
					font-size: 20rpx;
					margin-top: 12rpx;
				}
				&.bg1{
					background: #e8f6fe url("/static/image/index/p2.png") bottom right no-repeat;
					background-size: 137rpx 80rpx;
				}
				&.bg2{
					background: #e8f6fe url("/static/image/index/p3.png") bottom right no-repeat;
					background-size: 80rpx 83rpx;
				}
				&.bg3{
					background: #e8f6fe url("/static/image/index/p4.png") bottom right no-repeat;
					background-size: 194rpx 105rpx;
				}
				&.bg4{
					background: #e8f6fe url("/static/image/index/p5.png") bottom right no-repeat;
					background-size: 90rpx 81rpx;
				}
			}
		}
	}
</style>