{"level":"dev.info","ts":"[2025-08-05 09:29:32.684]","caller":"repayment/service.go:417","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:29:58.335]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:30:19.828]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:31:14.119]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:32:35.576]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:33:25.500]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:33:27.226]","caller":"repayment/service.go:881","msg":"还款加锁成功","lock_key":"repayment:bill:649","bill_id":649,"action":"repayment_lock_acquired"}
{"level":"dev.error","ts":"[2025-08-05 09:33:39.657]","caller":"repayment/service.go:504","msg":"创建资管费支付失败","error":"支付失败: 签名验证失败[EG000001]","bill_id":649,"amount":366.22,"payment_type":"asset_management","stacktrace":"fincore/app/business/repayment.(*PaymentService).createRepaymentInternal\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:504\nfincore/app/business/repayment.(*PaymentService).CreateRepayment\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:426\nfincore/app/uniapp/repayment.(*PaymentController).CreateRepayment\n\tD:/work/code/fincore/go/src/app/uniapp/repayment/controller.go:83\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:33:39.657]","caller":"repayment/service.go:881","msg":"还款加锁成功","lock_key":"repayment:bill:649","bill_id":649,"action":"repayment_lock_acquired"}
{"level":"dev.info","ts":"[2025-08-05 09:36:34.094]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:36:35.871]","caller":"repayment/service.go:881","msg":"还款加锁成功","lock_key":"repayment:bill:649","bill_id":649,"action":"repayment_lock_acquired"}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.413]","caller":"repayment/service.go:756","msg":"开始更新交易状态","transaction_no":"RP1754357795902500624","status":1,"channel_transaction_no":"*********","third_party_order_no":"RP1754357795902500624","action":"update_transaction_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.425]","caller":"repayment/service.go:778","msg":"更新交易状态成功","transaction_no":"RP1754357795902500624","status":1,"action":"update_transaction_status_success"}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.425]","caller":"repayment/service.go:1048","msg":"支付请求提交成功","transaction_no":"RP1754357795902500624","third_party_order_no":"RP1754357795902500624","channel_transaction_no":"*********","status":"2","action":"payment_request_submitted"}
{"level":"dev.info","ts":"[2025-08-05 09:36:53.425]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754357795902500624","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.442]","caller":"repayment/service.go:416","msg":"开始处理还款支付","bill_id":649,"bank_card_id":74,"action":"create_repayment_start"}
{"level":"dev.info","ts":"[2025-08-05 09:37:14.493]","caller":"repayment/service.go:483","msg":"资管费已全部支付","bill_id":649,"amount":0,"payment_type":"asset_management"}
{"level":"dev.info","ts":"[2025-08-05 09:44:00.047]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:45:59.855]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:47:19.009]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:48:05.264]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:48:10.910]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/business/repayment.(*Manager).GetPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/controller.go:115\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.029]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.029]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.029]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.049]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.049]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.049]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.234]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.234]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.234]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.265]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.265]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.266]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:48:11.425]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.425]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.609]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:48:11.640]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.049]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.257]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.257]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.257]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.290]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.290]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.290]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.471]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.471]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.471]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.512]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.512]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.512]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:50:00.656]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.656]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.840]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:50:00.867]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.038]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.228]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.228]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.228]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.252]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.252]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.252]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.422]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.422]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.422]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.449]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.449]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.449]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:52:00.581]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.582]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.769]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:52:00.819]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.057]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.291]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.291]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.291]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.318]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.318]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.318]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.498]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.498]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.498]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.520]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.520]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.520]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:54:00.693]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.693]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.866]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:54:00.904]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.089]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.292]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.292]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.292]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.323]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.323]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.323]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.479]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.479]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.479]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.502]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.502]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.502]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:56:00.657]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.657]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.825]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:56:00.857]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.054]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.247]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.247]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.247]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.302]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.302]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.302]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.507]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.507]","caller":"repayment/service.go:166","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.507]","caller":"repayment/service.go:253","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.547]","caller":"repayment/service.go:281","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:281\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:168\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.547]","caller":"repayment/service.go:175","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:175\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.547]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:58:00.715]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.715]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358203898784233","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.899]","caller":"repayment/service.go:155","msg":"处理第三方支付状态","transaction_no":"RP1754358203898784233","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 09:58:00.932]","caller":"repayment/service.go:227","msg":"查询支付状态完成","transaction_no":"RP1754358203898784233","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 09:58:20.580]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.error","ts":"[2025-08-05 09:58:41.783]","caller":"repayment/service.go:139","msg":"第三方查询失败","transaction_no":"RP1754358122877590963","third_party_order_no":"RP1754358122877590963","resp_code":"tant004","resp_msg":"签名验证失败[EG000001]","action":"third_party_query_failed","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:139\nfincore/app/business/repayment.(*Manager).GetPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/controller.go:115\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-05 09:59:36.430]","caller":"repayment/service.go:71","msg":"开始查询支付状态","transaction_no":"RP1754358122877590963","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:18:00.064]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.056]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.057]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.057]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.083]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.083]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.083]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.256]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.256]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:18:11.256]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.280]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:18:11.280]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.072]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.251]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.251]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.251]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.278]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.278]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.278]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.473]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.473]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:20:00.473]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.587]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:20:00.587]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.074]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.285]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.285]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.285]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.311]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.311]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.311]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.487]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.487]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:24:00.487]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.510]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:24:00.510]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.062]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.240]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.240]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.240]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.267]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.267]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.267]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.487]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.487]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:26:00.487]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.524]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:26:00.524]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.060]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.276]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.276]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.276]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.313]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.313]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.313]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.493]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.493]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.493]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.536]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:28:00.536]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.536]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754364402669755512","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.744]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754364402669755512","third_party_status":"2","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:28:00.790]","caller":"repayment/service.go:226","msg":"查询支付状态完成","transaction_no":"RP1754364402669755512","final_status":"submitted","action":"query_payment_status_complete"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.260]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.515]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.515]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.515]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:38:00.611]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:38:00.611]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:38:00.611]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.117]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.117]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:38:01.117]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:38:01.322]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:38:01.322]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.105]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.335]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.335]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.335]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.386]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.386]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.386]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.568]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.568]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:40:00.568]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.621]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:40:00.621]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.070]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.305]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.305]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.305]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.345]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.345]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.345]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.538]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.538]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:42:00.538]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.587]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:42:00.587]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.062]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.273]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.273]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.273]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.323]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.323]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.323]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.492]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.492]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:44:00.492]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.549]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:44:00.549]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.167]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.428]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.428]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.428]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.502]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.502]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.502]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.777]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.777]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:52:00.777]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.804]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:52:00.804]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.080]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.303]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.303]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.303]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.336]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.336]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.337]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.541]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.541]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:54:00.541]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.584]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:54:00.584]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.056]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.271]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.271]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.271]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.307]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.307]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.308]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.511]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.511]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 11:56:00.511]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.563]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 11:56:00.563]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.050]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.259]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.259]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.259]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.283]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.283]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.284]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.464]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.464]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 12:04:00.464]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.488]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 12:04:00.488]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.040]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754297885279933166","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.268]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754297885279933166","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.268]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754297885279933166","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.269]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":583,"paid_amount":1.02,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.312]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":583,"order_id":148,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.312]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754297885279933166","bill_id":583,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.312]","caller":"repayment/service.go:70","msg":"开始查询支付状态","transaction_no":"RP1754298853035585483","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.490]","caller":"repayment/service.go:154","msg":"处理第三方支付状态","transaction_no":"RP1754298853035585483","third_party_status":"1","action":"process_third_party_status"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.490]","caller":"repayment/service.go:165","msg":"支付成功","transaction_no":"RP1754298853035585483","action":"payment_success"}
{"level":"dev.info","ts":"[2025-08-05 14:14:00.490]","caller":"repayment/service.go:252","msg":"开始更新账单和订单已还金额","bill_id":593,"paid_amount":366.22,"action":"update_bill_order_amount_start"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.524]","caller":"repayment/service.go:280","msg":"查询订单失败","bill_id":593,"order_id":149,"action":"get_order_failed","error":"订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).updateBillAndOrderPaidAmount\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:280\nfincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:167\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-05 14:14:00.524]","caller":"repayment/service.go:174","msg":"更新账单和订单已还金额失败","transaction_no":"RP1754298853035585483","bill_id":593,"action":"update_bill_amount_failed","error":"查询订单失败: 订单不存在","stacktrace":"fincore/app/business/repayment.(*PaymentService).QueryPaymentStatus\n\tD:/work/code/fincore/go/src/app/business/repayment/service.go:174\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).syncTransactionStatus\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:208\nfincore/app/scheduler/tasks/repayment.(*RepaymentStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/repayment/repayment_status_sync_compensation.go:118\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
