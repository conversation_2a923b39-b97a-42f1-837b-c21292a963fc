// @/api/user.js
import request from '@/utils/request';

export default {
	// 账号登录
	login: (data) =>
		request({
			url: '/user/login',
			method: 'POST',
			data,
			custom: {
				showSuccess: true,
				loadingMsg: '登录中',
			},
		}),
	// 获取用户信息
	getuserinfo: () =>
		request({
			url: '/user/getUserinfo',
			method: 'GET',
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 人脸识别结果更新
	getCheckFaceResult: (params) =>
		request({
			url: '/identity/getCheckFaceResult',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 人脸识别照片
	getFacePhoto: (params) =>
		request({
			url: '/identity/getFacePhoto',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 获取身份证认证状态
	getIdCardAuthStatus: () =>
		request({
			url: '/user/getIdCardAuthStatus',
			method: 'GET',
			custom: {
				auth: true,
			},
		}),
	// 账号登出
	logout: (data) =>
		request({
			url: '/user/logout',
			method: 'POST',
			data,
			custom: {
				auth: true,
			},
		}),
	sendSms: (data) =>
		request({
			url: '/user/postSms',
			method: 'POST',
			data,
		}),
	// 登录通过短信验证码
	loginBySms: (data) =>
		request({
			url: '/user/postBySms',
			method: 'POST',
			data,
			custom: {
				showSuccess: false,
				loadingMsg: '发送中',
			},
		}),
	// 提交个人信息
	postUserInfo: (data) => request({
		url: '/user/postBindInfo',
		method: 'POST',
		data,
		custom: {
			loadingMsg: '提交中...',
			showSuccess: true,
			auth: true,
		}
	}),
	// 提交个人信息
	postUserInfo2: (data) => request({
		url: '/bankcard/bankcardcontroller/postBindInfo',
		method: 'POST',
		data,
		custom: {
			loadingMsg: '提交中...',
			showSuccess: true,
			auth: true,
		}
	}),
	// 获取授信评估-是否认证通过  /uniapp/risk/riskcontroller/getEvaluate
	getEvaluate: (params) =>
		request({
			url: '/risk/riskcontroller/getEvaluate',
			method: 'GET',
			data: params,
			custom: {
				showError: false,
				auth: true,
			},
		}),
	// 获取授信额度&贷款产品
	getProducts: (params) =>
		request({
			url: '/risk/riskcontroller/getProducts',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 获取产品还款计划
	getRepaymentPreview: (params) =>
		request({
			url: '/order/get_repayment_preview',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 发起合同
	createContract: (params) =>
		request({
			url: '/hetong/createContract',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 获取合同状态
	queryContractStatus: (params) =>
		request({
			url: '/hetong/queryContractStatus',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 更新合同
	updateContract: (params) =>
		request({
			url: '/hetong/updateContract',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 检查用户是否可以创建订单
	checkCanCreate: (params) =>
		request({
			url: '/order/checkCanCreate',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 创建订单
	createOrder: (params) =>
		request({
			url: '/order/createOrder',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
		
	// 还款页 /uniapp/order/get_order_bills
	getOrderBills: (params) =>
		request({
			url: '/order/get_order_bills',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
		
	// 借还记录  /uniapp/order/get_user_order_history
	getUserOrderHistory: (params) =>
		request({
			url: '/order/get_user_order_history',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 注销账户 /uniapp/user/cancelAccount
	cancelAccount: (params) =>
		request({
			url: '/user/cancelAccount',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 客户还款 /uniapp/repayment/paymentcontroller/createRepayment
	createRepayment: (params) =>
		request({
			url: '/repayment/paymentcontroller/createRepayment',
			method: 'POST',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
	// 查询支付状态 /uniapp/repayment/manager/getPaymentStatus
	getPaymentStatus: (params) =>
		request({
			url: '/repayment/paymentcontroller/getPaymentStatus',
			method: 'GET',
			data: params,
			custom: {
				showLoading: true,
				auth: true,
			},
		}),
};