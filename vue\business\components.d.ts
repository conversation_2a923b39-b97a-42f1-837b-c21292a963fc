// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    BasicHelp: typeof import('./src/components/Basic/src/BasicHelp.vue')['default']
    BasicModal: typeof import('./src/components/Modal/src/BasicModal.vue')['default']
    BasicTitle: typeof import('./src/components/Basic/src/BasicTitle.vue')['default']
    Block: typeof import('./src/components/global-setting/block.vue')['default']
    Breadcrumb: typeof import('./src/components/breadcrumb/index.vue')['default']
    Button: typeof import('./src/components/gfeditor/toolbar/components/button.vue')['default']
    Chart: typeof import('./src/components/chart/index.vue')['default']
    CodeEditor: typeof import('./src/components/CodeEditor/src/CodeEditor.vue')['default']
    CodeMirror: typeof import('./src/components/CodeEditor/src/codemirror/CodeMirror.vue')['default']
    Collapse: typeof import('./src/components/gfeditor/toolbar/components/collapse/collapse.vue')['default']
    Color: typeof import('./src/components/gfeditor/toolbar/components/color/color.vue')['default']
    Component: typeof import('./src/components/gfeditor/plugin/codeblock-vue/component/select/component.vue')['default']
    Dropdown: typeof import('./src/components/gfeditor/toolbar/components/dropdown.vue')['default']
    DropdownList: typeof import('./src/components/gfeditor/toolbar/components/dropdown-list.vue')['default']
    Editor: typeof import('./src/components/gfeditor/plugin/imgattachment/editor.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    Footer: typeof import('./src/components/footer/index.vue')['default']
    FormWrapper: typeof import('./src/components/global-setting/form-wrapper.vue')['default']
    GlobalSetting: typeof import('./src/components/global-setting/index.vue')['default']
    Group: typeof import('./src/components/gfeditor/toolbar/components/group.vue')['default']
    Icon: typeof import('./src/components/Icon/src/Icon.vue')['default']
    IconPicker: typeof import('./src/components/Icon/src/IconPicker.vue')['default']
    Item: typeof import('./src/components/gfeditor/toolbar/components/collapse/item.vue')['default']
    JsonPreview: typeof import('./src/components/CodeEditor/src/json-preview/JsonPreview.vue')['default']
    List: typeof import('./src/components/message-box/list.vue')['default']
    Loading: typeof import('./src/components/Editor/Loading.vue')['default']
    Main: typeof import('./src/components/Editor/Main.vue')['default']
    Menu: typeof import('./src/components/menu/index.vue')['default']
    MessageBox: typeof import('./src/components/message-box/index.vue')['default']
    ModalClose: typeof import('./src/components/Modal/src/components/ModalClose.vue')['default']
    ModalFooter: typeof import('./src/components/Modal/src/components/ModalFooter.vue')['default']
    ModalHeader: typeof import('./src/components/Modal/src/components/ModalHeader.vue')['default']
    ModalWrapper: typeof import('./src/components/Modal/src/components/ModalWrapper.vue')['default']
    Navbar: typeof import('./src/components/navbar/index.vue')['default']
    Picker: typeof import('./src/components/gfeditor/toolbar/components/color/picker/picker.vue')['default']
    Preview: typeof import('./src/components/gfeditor/plugin/link-vue/toolbar/preview.vue')['default']
    RiskControlReport: typeof import('./src/components/riskControlReport/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/Icon/src/SvgIcon.vue')['default']
    TabBar: typeof import('./src/components/tab-bar/index.vue')['default']
    TabItem: typeof import('./src/components/tab-bar/tab-item.vue')['default']
    Table: typeof import('./src/components/gfeditor/toolbar/components/table.vue')['default']
    Toolbar: typeof import('./src/components/gfeditor/toolbar/components/toolbar.vue')['default']
  }
}
