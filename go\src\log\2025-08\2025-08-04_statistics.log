{"level":"dev.info","ts":"[2025-08-04 15:03:26.334]","caller":"statistics/service.go:207","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.error","ts":"[2025-08-04 15:03:26.354]","caller":"statistics/service.go:212","msg":"获取渠道统计列表失败","error":"查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:212\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:04:22.501]","caller":"statistics/service.go:207","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.error","ts":"[2025-08-04 15:04:22.508]","caller":"statistics/service.go:212","msg":"获取渠道统计列表失败","error":"查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:212\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:07:26.528]","caller":"statistics/service.go:207","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.error","ts":"[2025-08-04 15:07:26.534]","caller":"statistics/service.go:212","msg":"获取渠道统计列表失败","error":"查询渠道统计总数失败: Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:212\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:14:03.408]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:14:03.436]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:14:26.932]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:14:27.008]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:14:36.784]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","pageSize":"10"}}
{"level":"dev.error","ts":"[2025-08-04 15:14:36.821]","caller":"statistics/service.go:221","msg":"获取渠道统计列表失败","error":"查询渠道统计列表失败: 获取总记录数失败: sql: expected 2 arguments, got 1","stacktrace":"fincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:221\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:16:11.570]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:16:11.596]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:16:20.179]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:16:20.208]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:16:28.213]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024/08/04","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:16:28.237]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:17:23.153]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"invalid-date","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:17:23.268]","caller":"statistics/service.go:225","msg":"获取渠道统计列表成功","total":0,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:24:07.697]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:24:07.717]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:25:31.231]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"5"}}
{"level":"dev.info","ts":"[2025-08-04 15:25:31.311]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":5}
{"level":"dev.info","ts":"[2025-08-04 15:27:21.451]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":1,"pageSize":10}}
{"level":"dev.info","ts":"[2025-08-04 15:27:21.487]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:30.549]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:27:30.581]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:34.144]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"2","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:27:34.217]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:39.532]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:27:39.681]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:46.382]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:27:46.434]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:50.986]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","pageSize":"10"}}
{"level":"dev.info","ts":"[2025-08-04 15:27:51.105]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:32:46.926]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:32:46.965]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:34:03.152]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:34:03.176]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:34:11.708]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:34:11.742]","caller":"statistics/service.go:228","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:28.666]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"date":"2024-08-04","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:37:28.667]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:28.694]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":3,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:36.776]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:37:36.776]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:36.879]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:45.174]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:37:45.174]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:37:45.202]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:38:51.958]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:38:51.958]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:38:51.987]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:39:01.600]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"2","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:39:01.601]","caller":"statistics/service.go:215","msg":"分页参数","page":2,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:39:01.629]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:39:26.602]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:39:26.602]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:39:26.658]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:40:22.647]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:40:22.647]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:40:22.673]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:40:55.205]","caller":"statistics/service.go:210","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:40:55.205]","caller":"statistics/service.go:215","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:40:55.249]","caller":"statistics/service.go:236","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:17.865]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:45:17.865]","caller":"statistics/service.go:214","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:17.903]","caller":"statistics/service.go:232","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:26.593]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:45:26.594]","caller":"statistics/service.go:214","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:26.619]","caller":"statistics/service.go:232","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:28.160]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:45:28.160]","caller":"statistics/service.go:214","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:28.200]","caller":"statistics/service.go:232","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:46:14.228]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:46:14.228]","caller":"statistics/service.go:214","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:46:23.097]","caller":"statistics/service.go:232","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:46:47.881]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":10}}
{"level":"dev.info","ts":"[2025-08-04 15:46:55.587]","caller":"statistics/service.go:214","msg":"分页参数","page":1,"page_size":10}
{"level":"dev.info","ts":"[2025-08-04 15:46:55.619]","caller":"statistics/service.go:232","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-04 15:48:09.756]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":"20"}}
{"level":"dev.info","ts":"[2025-08-04 15:48:10.047]","caller":"statistics/service.go:231","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":20}
{"level":"dev.info","ts":"[2025-08-04 15:48:11.163]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":"20"}}
{"level":"dev.info","ts":"[2025-08-04 15:48:11.394]","caller":"statistics/service.go:231","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":20}
{"level":"dev.info","ts":"[2025-08-04 15:48:15.847]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":"50"}}
{"level":"dev.info","ts":"[2025-08-04 15:48:15.900]","caller":"statistics/service.go:231","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":50}
{"level":"dev.info","ts":"[2025-08-04 15:48:19.675]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"date":"2024-08-03","page":"1","page_size":"20"}}
{"level":"dev.info","ts":"[2025-08-04 15:48:19.704]","caller":"statistics/service.go:231","msg":"获取渠道统计列表成功","total":2,"page":1,"pageSize":20}
{"level":"dev.info","ts":"[2025-08-04 15:48:27.054]","caller":"statistics/service.go:209","msg":"开始获取渠道统计列表","params":{"page":"1","page_size":"20"}}
{"level":"dev.info","ts":"[2025-08-04 15:48:27.082]","caller":"statistics/service.go:231","msg":"获取渠道统计列表成功","total":5,"page":1,"pageSize":20}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.003]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.005]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.023]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.024]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs-0731-dq]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs_01]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[fincore]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[公众号]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[test]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs_03]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[gz111]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs_02]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[刺猬]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.001]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.001]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.016]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.error","ts":"[2025-08-04 16:26:52.751]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[刺猬]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:26:54.157]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[fincore]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:26:54.562]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs_03]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.error","ts":"[2025-08-04 16:26:54.730]","caller":"statistics/service.go:90","msg":"渠道统计出现错误","error":"渠道[cs_01]统计失败: 统计新用户数失败: 统计渠道新用户数失败: sql: expected 5 arguments, got 3","stacktrace":"fincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:90\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.002]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.003]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.056]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:28:23.233]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 16:28:23.233]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.280]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.280]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.284]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 16:28:23.769]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.290]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.290]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 16:28:29.290]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.045]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.048]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.164]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.182]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.182]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.181]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 16:30:01.375]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:30:01.375]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.001]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.002]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.000]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.017]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.017]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.017]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.018]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.018]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.001]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.001]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.042]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.042]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.000]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.001]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.031]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.032]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.002]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.002]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.071]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.071]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.002]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.003]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.022]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.022]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.002]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.003]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.029]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.029]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.478]","caller":"statistics/service.go:130","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.543]","caller":"statistics/service.go:145","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.001]","caller":"statistics/service.go:35","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.001]","caller":"statistics/service.go:39","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.031]","caller":"statistics/service.go:56","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.031]","caller":"statistics/service.go:107","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.003]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.004]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.095]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.109]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.120]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.120]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.144]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.155]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.155]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.156]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.156]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.179]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.179]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.229]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.229]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.238]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.238]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.245]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.040]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.151]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.163]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.195]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.207]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.218]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.219]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.225]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.233]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.242]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.001]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.002]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.021]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.022]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.023]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.092]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.127]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.129]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.140]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.157]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.157]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.164]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.172]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.177]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.001]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.048]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.048]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.048]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.049]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.124]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.125]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.125]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.133]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.134]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.134]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.158]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.178]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.182]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.188]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.188]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.197]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.000]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.014]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.014]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.015]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.073]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.093]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.093]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.093]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.107]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.123]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.127]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.139]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.139]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.139]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.142]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.142]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.145]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.149]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.001]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.027]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.027]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.027]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.027]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.027]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.028]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.028]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.028]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.028]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.028]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.143]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.148]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.155]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.155]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.169]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.176]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.184]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.185]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.185]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.185]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.192]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.192]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.198]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.000]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.020]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.020]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.020]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.020]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.021]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.059]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.059]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.060]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.064]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.065]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.075]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.075]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.076]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.079]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.079]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.080]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.083]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.084]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.098]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.104]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"statistics/service.go:36","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"statistics/service.go:40","msg":"统计时间范围","start_time":"2025-08-04 00:00:00","end_time":"2025-08-04 23:59:59"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:57","msg":"找到启用渠道","channel_count":9}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.012]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.012]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.012]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.012]","caller":"statistics/service.go:106","msg":"开始统计渠道数据","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.071]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":12,"channel_name":"cs-0731-dq","new_user_count":1,"real_name_count":0,"transaction_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.072]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":10,"channel_name":"cs_02","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.077]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":11,"channel_name":"cs_03","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.078]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":4,"channel_name":"刺猬","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.078]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":8,"channel_name":"gz111","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.081]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":9,"channel_name":"cs_01","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.090]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":3,"channel_name":"test","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.112]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":4,"channel_name":"刺猬"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.112]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":12,"channel_name":"cs-0731-dq"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.117]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":2,"channel_name":"公众号","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":11,"channel_name":"cs_03"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"statistics/service.go:129","msg":"渠道统计数据","channel_id":1,"channel_name":"fincore","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":8,"channel_name":"gz111"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":9,"channel_name":"cs_01"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.127]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":10,"channel_name":"cs_02"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.132]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":3,"channel_name":"test"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":2,"channel_name":"公众号"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/service.go:144","msg":"渠道统计完成","channel_id":1,"channel_name":"fincore"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"statistics/service.go:97","msg":"渠道统计任务执行完成","success_channel_count":9}
