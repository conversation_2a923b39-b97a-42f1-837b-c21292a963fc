{"level":"dev.info","ts":"[2025-08-04 11:15:41.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 ORDER BY baa.createtime DESC) as count_query, []","duration":"30.8136ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, []","duration":"31.8978ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"11.2649ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"40.7029ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 11:16:13.122]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND COALESCE(order_filter.loanTime, 0) >= UNIX_TIMESTAMP(?) AND COALESCE(order_filter.loanTime, 0) <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"12.6093ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:16:13.136]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND COALESCE(order_filter.loanTime, 0) >= UNIX_TIMESTAMP(?) AND COALESCE(order_filter.loanTime, 0) <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"14.9455ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 11:21:05.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"34.7581ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-04 11:21:05.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"31.0086ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 11:21:06.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"9.0541ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 11:21:06.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"16.5747ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:26:18.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"16.7829ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:26:18.829]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"56.1418ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-04 11:26:20.492]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"12.2097ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:26:20.505]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"13.6379ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 11:41:47.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 00:00:00 2025-08-02 23:59:59]","duration":"12.5164ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:41:47.568]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 00:00:00 2025-08-02 23:59:59]","duration":"26.6337ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 11:41:47.585]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"16.7964ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:41:47.607]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"38.72ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-04 11:41:57.240]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 00:00:00 2025-08-03 23:59:59]","duration":"16.7901ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:41:57.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 00:00:00 2025-08-03 23:59:59]","duration":"19.0411ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 11:41:57.284]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"24.0505ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 11:41:57.284]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"24.0505ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 11:42:03.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-03 00:00:00 2025-08-03 23:59:59]","duration":"21.0998ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 11:42:03.835]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-03 00:00:00 2025-08-03 23:59:59]","duration":"24.2961ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 11:44:41.431]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id LIMIT 1, []","duration":"19.3806ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 11:44:41.494]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id ORDER BY blo.id DESC LIMIT 20, []","duration":"61.2257ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-04 11:44:41.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (1)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"47.6789ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-04 11:44:57.830]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? LIMIT 1, [%ababa%]","duration":"57.1073ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-04 11:44:57.841]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? ORDER BY blo.id DESC LIMIT 20, [%ababa%]","duration":"9.1277ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 11:45:00.912]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? LIMIT 1, [%a%]","duration":"14.401ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 11:45:00.927]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? ORDER BY blo.id DESC LIMIT 20, [%a%]","duration":"14.7126ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 11:45:05.910]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? LIMIT 1, [%admin%]","duration":"12.1027ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:45:05.920]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE sales_user.name like ? ORDER BY blo.id DESC LIMIT 20, [%admin%]","duration":"10.4486ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 11:45:09.742]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id LIMIT 1, []","duration":"12.2461ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:45:09.757]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id ORDER BY blo.id DESC LIMIT 20, []","duration":"14.1332ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 11:45:09.765]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (1)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"8.7029ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 14:06:49.865]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id LIMIT 1, []","duration":"16.649ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 14:06:49.892]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id ORDER BY blo.id DESC LIMIT 20, []","duration":"25.2901ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 14:06:49.901]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (1)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"7.869ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 14:07:08.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-03 00:00:00 2025-08-03 23:59:59]","duration":"13.6ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 14:07:13.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-03 00:00:00 2025-08-03 23:59:59]","duration":"124.9671ms","duration_ms":124}
{"level":"dev.error","ts":"[2025-08-04 15:03:26.353]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"18587e602927950c4fac100e","error_message":"Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetChannelStatisticsListByParams\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:183\nfincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:210\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-04 15:04:22.507]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"18587e6d3d0a86802297ef54","error_message":"Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetChannelStatisticsListByParams\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:183\nfincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:210\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-04 15:07:26.533]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"Error 1146 (42S02): Table 'fincore.channel_statistics' doesn't exist","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetChannelStatisticsListByParams\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:183\nfincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:210\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:14:03.419]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"10.6168ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 15:14:03.436]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"14.9792ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 15:14:26.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"25.0712ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 15:14:27.008]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"51.6037ms","duration_ms":51}
{"level":"dev.error","ts":"[2025-08-04 15:14:36.820]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 2 arguments, got 1","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/utils/pagination.PaginateWithCustomQuery\n\tD:/work/code/fincore/go/src/utils/pagination/pagination.go:119\nfincore/app/business/statistics.(*Repository).GetChannelStatisticsListByParams\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:169\nfincore/app/business/statistics.(*Service).GetChannelStatisticsList\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:219\nfincore/app/business/statistics.(*StatisticsController).GetChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/controller.go:49\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.Bind.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.ErrorLogMiddleware.func5\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.AccessLogMiddleware.func4\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-04 15:16:11.584]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"13.5263ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:16:11.596]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"12.8859ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:16:20.198]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"18.8209ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 15:16:20.208]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"9.8555ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 15:16:28.225]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024/08/04]","duration":"11.3096ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 15:16:28.237]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024/08/04]","duration":"12.0844ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:17:23.260]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [invalid-date]","duration":"106.8151ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-08-04 15:17:23.268]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [invalid-date]","duration":"7.9426ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 15:24:07.708]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"10.1493ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 15:24:07.717]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"9.197ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 15:25:31.286]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"54.9156ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-04 15:25:31.311]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 5, []","duration":"23.4789ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 15:27:21.472]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"20.2409ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 15:27:21.486]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"14.1744ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 15:27:30.563]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"13.8222ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:27:30.581]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"16.9572ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 15:27:34.206]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"62.1142ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-04 15:27:34.217]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10 OFFSET 10, []","duration":"10.5715ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:39.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"22.015ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 15:27:39.680]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"125.4913ms","duration_ms":125}
{"level":"dev.info","ts":"[2025-08-04 15:27:46.412]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"29.4448ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 15:27:46.434]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"20.8985ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 15:27:51.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"90.3204ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-08-04 15:27:51.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"27.4026ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 15:32:46.951]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"25.382ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 15:32:46.964]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"12.181ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:34:03.165]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"13.0824ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:34:03.176]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"9.7538ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 15:34:11.719]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"10.3117ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 15:34:11.742]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"23.6094ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 15:37:28.685]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-04]","duration":"18.3247ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 15:37:28.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-04]","duration":"8.0115ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 15:37:36.830]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"54.3105ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-04 15:37:36.879]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"48.3745ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-04 15:37:45.188]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"13.2995ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:37:45.202]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"13.5665ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:38:51.972]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"14.0944ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 15:38:51.986]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"13.5968ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:39:01.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"18.7157ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 15:39:01.629]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10 OFFSET 10, []","duration":"9.3025ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 15:39:26.635]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"33.0885ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-04 15:39:26.658]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"23.2967ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 15:40:22.660]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"12.2446ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:40:22.672]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"12.1346ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:40:55.237]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"32.1934ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-04 15:40:55.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"11.6043ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 15:45:17.878]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"12.0589ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:45:17.903]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"25.4585ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 15:45:26.607]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"13.5912ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:45:26.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"12.0828ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 15:45:28.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"16.8984ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 15:45:28.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"23.4149ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 15:46:23.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"55.8779ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-04 15:46:23.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"36.9462ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 15:46:55.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"13.7634ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 15:46:55.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2024-08-03]","duration":"17.1661ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 15:48:09.836]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d0f1911794a1069539","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"79.329ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-04 15:48:10.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d0f1911794a1069539","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 20, [2024-08-03]","duration":"211.0397ms","duration_ms":211}
{"level":"dev.info","ts":"[2025-08-04 15:48:11.204]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d145827744af4de055","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"40.0744ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 15:48:11.394]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d145827744af4de055","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 20, [2024-08-03]","duration":"189.9646ms","duration_ms":189}
{"level":"dev.info","ts":"[2025-08-04 15:48:15.875]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d25cb9d474b895612b","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"27.4054ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 15:48:15.900]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d25cb9d474b895612b","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 50, [2024-08-03]","duration":"24.9195ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 15:48:19.685]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d340e4496868d9f4f6","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2024-08-03]","duration":"9.9024ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 15:48:19.704]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d340e4496868d9f4f6","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 20, [2024-08-03]","duration":"18.3141ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 15:48:27.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d4f8afa12ca8efd012","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"15.4594ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 15:48:27.082]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185880d4f8afa12ca8efd012","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 20, []","duration":"12.2134ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.023]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"17.4978ms","duration_ms":17}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.030]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.082]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.082]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.082]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.083]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.092]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.092]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:22:50.104]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.013]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"11.5484ms","duration_ms":11}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.028]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.078]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.078]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.078]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.078]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.078]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.086]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.086]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.error","ts":"[2025-08-04 16:26:30.086]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"51.8153ms","duration_ms":51}
{"level":"dev.error","ts":"[2025-08-04 16:28:29.302]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.159]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"110.089ms","duration_ms":110}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"15.3071ms","duration_ms":15}
{"level":"dev.error","ts":"[2025-08-04 16:39:50.033]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 6 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:112\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"40.5946ms","duration_ms":40}
{"level":"dev.error","ts":"[2025-08-04 16:42:35.936]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:113\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"29.4775ms","duration_ms":29}
{"level":"dev.error","ts":"[2025-08-04 16:43:13.111]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 5 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:113\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"30.3464ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"67.6977ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-04 16:45:04.753]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"4.7495311s","duration_ms":4749}
{"level":"dev.info","ts":"[2025-08-04 16:45:04.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? LIMIT 1, [1]","duration":"10.2678ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.021]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"17.8587ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 16:46:23.257]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` BETWEEN ? and ? LIMIT 1, [1 ********** **********]","duration":"19.2367ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.027]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"23.433ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 16:55:32.348]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"15.8584ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"31.6904ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.478]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"34.4072ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.516]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"37.8644ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-04 16:55:36.543]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`created_at`,`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`) VALUES (?,?,?,?,?), [2025-08-04 16:55:36.516905 +0800 CST m=+10.335270601 1 0 0 0]","duration":"26.9643ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.031]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"29.1017ms","duration_ms":29}
{"level":"dev.error","ts":"[2025-08-04 16:57:14.674]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","error_message":"sql: expected 6 arguments, got 3","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:172\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).First\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:30\nfincore/utils/gform.(*Orm)._unionBuild\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:92\nfincore/utils/gform.(*Orm).Count\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:60\nfincore/app/business/statistics.(*Repository).GetNewUserCountByChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:51\nfincore/app/business/statistics.(*Service).statisticsForChannel\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:113\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:74\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"17.5371ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"21.7284ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.073]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"28.4855ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.078]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"54.7157ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.078]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"55.7326ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.078]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"54.7157ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"67.0521ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"68.6965ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"67.173ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"68.6965ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"68.6965ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.1686ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.1686ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.6868ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.5209ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.8765ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"14.907ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"14.907ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.9986ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"13.9986ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.9986ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.3999ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.0697ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.0697ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.144]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"38.4801ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"44.959ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`) VALUES (?,?,?,?,?), [0 0 0 2025-08-04 17:04:50.1099852 +0800 CST m=+4.347123801 2]","duration":"44.959ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"49.7715ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"50.417ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"46.0891ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.171]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"51.2275ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.171]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"51.2275ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"27.4883ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"21.9775ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"22.4823ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.6029ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [11 0 0 0 2025-08-04 17:04:50.1554477 +0800 CST m=+4.392586301]","duration":"23.1089ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.179]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"22.9944ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.229]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`,`new_customer_reg_num`) VALUES (?,?,?,?,?), [0 0 2025-08-04 17:04:50.1714489 +0800 CST m=+4.408587501 3 0]","duration":"58.0713ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.229]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`,`new_customer_reg_num`) VALUES (?,?,?,?,?), [0 2 2025-08-04 17:04:50.1721506 +0800 CST m=+4.409289201 12 1]","duration":"56.8659ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.237]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"57.8367ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [10 0 0 0 2025-08-04 17:04:50.1780518 +0800 CST m=+4.415190401]","duration":"60.0687ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`) VALUES (?,?,?,?,?), [0 0 0 2025-08-04 17:04:50.1785566 +0800 CST m=+4.415695201 9]","duration":"59.5566ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [4 0 0 0 2025-08-04 17:04:50.1714489 +0800 CST m=+4.408587501]","duration":"67.1764ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.256]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 1 2025-08-04]","duration":"19.1005ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`created_at`,`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`) VALUES (?,?,?,?,?), [2025-08-04 17:04:50.2386253 +0800 CST m=+4.475763901 8 0 0 0]","duration":"18.4859ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.033]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"25.5195ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"39.1466ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"38.236ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"39.2338ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"40.2443ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"59.6136ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"59.1092ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.113]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"33.9217ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"54.8403ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"34.9649ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.150]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"51.5025ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"52.0085ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"52.0085ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"52.0085ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"37.027ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.162]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"29.0168ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.163]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"28.3593ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.163]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"28.8963ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"26.4685ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.98ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.98ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.4685ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.4849ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"26.0338ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.0338ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.5397ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.189]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.0338ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.195]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [1 0 2 12 2025-08-04]","duration":"16.8302ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.195]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"16.3253ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.196]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"17.4997ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.196]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"17.4997ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"17.7011ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"17.7011ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 1 2025-08-04]","duration":"17.1976ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"17.7011ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.218]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 3 2025-08-04]","duration":"22.4885ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.218]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"29.0807ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.218]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 9 2025-08-04]","duration":"23.6629ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.225]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 4 2025-08-04]","duration":"17.8487ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.230]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 10 2025-08-04]","duration":"23.1245ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.233]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2 2025-08-04]","duration":"26.4584ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 8 2025-08-04]","duration":"22.6795ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 11 2025-08-04]","duration":"29.3768ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"17.7145ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"22.4964ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.9269ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"55.4766ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"57.0507ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"56.5475ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"56.5475ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"58.0647ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"62.8153ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"61.6073ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"62.9872ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.6853ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.103]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"23.2791ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.8569ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"29.6282ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"28.6142ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.3524ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"29.6904ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"29.1247ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"21.477ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"35.0805ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"19.0437ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"23.8517ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.140]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"31.4335ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"39.1828ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"39.1828ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`,`new_customer_reg_num`) VALUES (?,?,?,?,?), [0 0 2025-08-04 17:06:20.1277374 +0800 CST m=+2.945456801 1 0]","duration":"20.1391ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"39.1828ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"15.6881ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"29.5907ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"28.0849ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"28.0849ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.164]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"16.9176ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`created_at`,`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`) VALUES (?,?,?,?,?), [2025-08-04 17:06:20.1558153 +0800 CST m=+2.973534701 3 0 0 0]","duration":"15.1396ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"24.5912ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"24.5912ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"20.1869ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [8 0 0 0 2025-08-04 17:06:20.1573281 +0800 CST m=+2.975047501]","duration":"20.1869ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"12.7209ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"20.1869ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [4 0 0 0 2025-08-04 17:06:20.1724677 +0800 CST m=+2.990187101]","duration":"15.3026ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`) VALUES (?,?,?,?,?), [0 0 0 2025-08-04 17:06:20.1724677 +0800 CST m=+2.990187101 2]","duration":"15.3026ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.187]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`created_at`,`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`) VALUES (?,?,?,?,?), [2025-08-04 17:06:20.1716929 +0800 CST m=+2.989412301 11 0 0 0]","duration":"16.0774ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`,`channel_id`) VALUES (?,?,?,?,?), [0 0 0 2025-08-04 17:06:20.177515 +0800 CST m=+2.********* 9]","duration":"22.5985ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`created_at`,`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`) VALUES (?,?,?,?,?), [2025-08-04 17:06:20.177515 +0800 CST m=+2.********* 12 1 0 2]","duration":"22.5985ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `channel_statistics` (`channel_id`,`new_customer_reg_num`,`real_name_num`,`number_of_transactions`,`created_at`) VALUES (?,?,?,?,?), [10 0 0 0 2025-08-04 17:06:20.177515 +0800 CST m=+2.*********]","duration":"21.0506ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"47.2845ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"22.9107ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"22.9107ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"22.9107ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"22.9107ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.073]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"23.9268ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"30.6228ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"40.1167ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"40.1167ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.1687ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.8348ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.9475ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.1687ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"25.8138ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.100]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"50.2458ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"20.4343ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"20.4343ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"23.976ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"23.976ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.124]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"24.0465ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.125]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"24.0465ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.125]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"25.2502ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.1849ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.6957ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.7076ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.139]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"14.3179ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"34.4162ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"32.5819ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"41.8939ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"42.4696ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"32.9349ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.166]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"32.4177ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 4 2025-08-04]","duration":"14.9426ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 11 2025-08-04]","duration":"33.7652ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"39.3053ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"15.4463ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.174]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"39.8089ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [1 0 2 12 2025-08-04]","duration":"11.9889ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.181]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"14.0882ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.182]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 1 2025-08-04]","duration":"14.5937ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.182]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"15.3744ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 3 2025-08-04]","duration":"13.4252ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.188]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 10 2025-08-04]","duration":"13.9341ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2 2025-08-04]","duration":"13.4252ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.197]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 8 2025-08-04]","duration":"15.5537ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 9 2025-08-04]","duration":"18.6022ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.014]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"13.2716ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.034]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"18.9999ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"20.6055ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"20.0134ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"20.5195ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"21.0632ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"20.5571ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"20.5571ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"20.5571ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"21.5758ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.5117ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.9618ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.4744ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.9618ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"19.0316ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.519ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.519ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"19.5737ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"20.8302ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.073]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.8974ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"36.3125ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"37.3912ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"36.8491ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"37.3912ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"36.3125ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"25.6377ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"36.9976ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"36.9976ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.104]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"31.1822ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.119]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"25.9478ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.119]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"27.2383ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"30.0287ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"30.0287ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"30.0287ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"30.7138ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 8 2025-08-04]","duration":"22.8232ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"24.6399ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 4 2025-08-04]","duration":"19.6894ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.139]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 1 2025-08-04]","duration":"19.5894ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.139]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2 2025-08-04]","duration":"16.1139ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 9 2025-08-04]","duration":"19.2181ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [1 0 2 12 2025-08-04]","duration":"18.7146ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.145]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"22.1574ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.145]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 11 2025-08-04]","duration":"22.1574ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.149]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 3 2025-08-04]","duration":"16.4645ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 10 2025-08-04]","duration":"9.5109ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"25.4396ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"24.0631ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"43.8668ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"77.6933ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"78.9127ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.115]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"87.0505ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.116]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"87.6966ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"88.9037ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"89.4088ms","duration_ms":89}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"88.2441ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.0444ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"88.2441ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"19.4924ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"19.4924ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"14.6922ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.2577ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.8848ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.2577ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"25.5172ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"16.0446ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"16.5828ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.148]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [2025-08-04 17:09:40.1323952 +0800 CST m=+10.********* 1 0 2 12 2025-08-04]","duration":"15.2796ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.5103ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"22.5103ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"15.7067ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.7067ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"15.7067ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.5317ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.167]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"12.1903ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.169]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"13.9444ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.169]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.9444ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.175]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"16.6002ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.176]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:40.1589269 +0800 CST m=+10.085809401 0 2 2025-08-04]","duration":"17.1832ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.176]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"17.1832ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"14.0877ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-04 17:09:40.1698656 +0800 CST m=+10.096748101 0 0 8 2025-08-04]","duration":"14.5977ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:40.1683133 +0800 CST m=+10.095195801 9 2025-08-04]","duration":"16.2008ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"14.5977ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.185]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.0912ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.192]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:40.1755271 +0800 CST m=+10.102409601 0 4 2025-08-04]","duration":"16.6155ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.192]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:40.1761101 +0800 CST m=+10.102992601 3 2025-08-04]","duration":"16.0325ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.198]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"13.7881ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.198]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:40.1845141 +0800 CST m=+10.111396601 11 2025-08-04]","duration":"14.2921ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.198]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"13.7881ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:40.199313 +0800 CST m=+10.126195501 10 2025-08-04]","duration":"10.3376ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-04 17:09:40.1988062 +0800 CST m=+10.********* 0 0 1 2025-08-04]","duration":"11.047ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"19.6146ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"16.0337ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"16.0337ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"16.6507ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"16.0337ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"16.0337ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"16.6507ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"16.6507ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"20.7395ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"20.7395ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.1965ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.1965ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.7001ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.7008ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.8155ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"12.4007ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"12.4007ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.8155ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.2153ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"7.1897ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"7.711ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.2153ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.7316ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"8.7316ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.0121ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"9.7078ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"9.7078ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.065]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"10.2206ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.065]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"10.2206ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"9.3073ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"9.3073ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"9.815ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:50.0645749 +0800 CST m=+19.991457401 0 3 2025-08-04]","duration":"10.496ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"10.496ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:50.0650877 +0800 CST m=+19.991970201 0 1 2025-08-04]","duration":"11.0037ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"12.0025ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [1 0 2 2025-08-04 17:09:50.0694026 +0800 CST m=+19.996285101 12 2025-08-04]","duration":"9.7294ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"14.0443ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-04 17:09:50.0694026 +0800 CST m=+19.996285101 0 0 10 2025-08-04]","duration":"10.615ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.083]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:50.0694026 +0800 CST m=+19.996285101 0 4 2025-08-04]","duration":"14.0326ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:50.0755837 +0800 CST m=+20.002466201 11 2025-08-04]","duration":"7.9237ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"7.9237ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.097]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"17.9535ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.097]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:50.0796607 +0800 CST m=+20.006543201 0 9 2025-08-04]","duration":"17.9535ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.104]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:09:50.0840151 +0800 CST m=+20.010897601 8 2025-08-04]","duration":"20.254ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:09:50.097683 +0800 CST m=+20.024565501 0 2 2025-08-04]","duration":"11.1344ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.010]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250804171000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"8.9766ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250804171000","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"10.5455ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"17.9494ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"18.0881ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [4 ********** **********]","duration":"14.0433ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [9 ********** **********]","duration":"14.0433ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [10 ********** **********]","duration":"14.0433ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [8 ********** **********]","duration":"14.0433ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [12 ********** **********]","duration":"15.9998ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"33.0568ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [11 ********** **********]","duration":"32.144ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [10 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"26.4789ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [12 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"25.3723ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [9 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.015ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [4 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"27.8339ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754297885*********]","duration":"36.1384ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [8 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"30.1507ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.058]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.9049ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.058]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [11 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.9049ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [12 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"17.1115ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"58.4557ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [10 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.6696ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [9 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.6696ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"64.7718ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [11 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.1756ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.078]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [4 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"18.6992ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.078]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [8 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"20.2884ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.082]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [12 2025-08-04]","duration":"10.5545ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [11 2025-08-04]","duration":"10.2422ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [4 2025-08-04]","duration":"10.9019ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"10.7658ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [10 2025-08-04]","duration":"16.1669ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"11.3038ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [8 2025-08-04]","duration":"16.7745ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [9 2025-08-04]","duration":"14.6752ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"13.6104ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-04]","duration":"21.3337ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:10:00.0899486 +0800 CST m=+30.********* 4 2025-08-04]","duration":"22.3455ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-04 17:10:00.0893812 +0800 CST m=+30.016263701 0 0 11 2025-08-04]","duration":"22.3455ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [1 0 2 2025-08-04 17:10:00.0893812 +0800 CST m=+30.016263701 12 2025-08-04]","duration":"22.3455ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"21.1079ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:10:00.09632 +0800 CST m=+30.023202501 9 2025-08-04]","duration":"23.8458ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-04 00:00:00 +0000 UTC 2025-08-04 23:59:59.********* +0000 UTC]","duration":"23.8458ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:10:00.0958014 +0800 CST m=+30.022683901 8 2025-08-04]","duration":"24.3644ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-04 17:10:00.1122941 +0800 CST m=+30.039176601 0 10 2025-08-04]","duration":"15.4297ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-04 17:10:00.1122941 +0800 CST m=+30.039176601 0 0 3 2025-08-04]","duration":"19.1988ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.135]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-04]","duration":"18.4118ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.136]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-04]","duration":"16.2163ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [2025-08-04 17:10:00.136364 +0800 CST m=+30.063246501 0 0 0 2 2025-08-04]","duration":"15.2914ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 0 2025-08-04 17:10:00.1368855 +0800 CST m=+30.063768001 1 2025-08-04]","duration":"15.2867ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.271]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`completed_at` = ? WHERE `transaction_no` = ?, [1 2025-08-04 17:10:00.2617447 +0800 CST m=+30.188627201 RP1754297885*********]","duration":"10.1724ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.301]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [RP1754297885*********]","duration":"29.1054ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.031]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250804172000","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label,\n\t\t\tba.availableProductID\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN business_app_account ba ON blo.user_id = ba.id\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\t  AND ba.availableProductID = 0\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"29.5629ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250804172000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"35.8418ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250804172000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"36.8752ms","duration_ms":36}
