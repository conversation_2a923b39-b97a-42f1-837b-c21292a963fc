/**
 * utils-request
 * @description api模块管理，loading配置，请求拦截，错误处理
 */
import { getCurrentInstance } from 'vue'; 
import Request from 'luch-request';
import { baseUrl, apiPath ,apiModel} from '@/utils/config';
// import $store from '@/store';
import user from "@/store/user.js";


import md5 from 'js-md5';
const base64Encode = (str) => {
  return wx.arrayBufferToBase64(new Uint8Array(str.split('').map(c => c.charCodeAt(0))))
}
const options = {
  // 显示操作成功消息 默认不显示
  showSuccess: false,
  // 成功提醒 默认使用后端返回值
  successMsg: '',
  // 显示失败消息 默认显示
  showError: true,
  // 失败提醒 默认使用后端返回信息
  errorMsg: '',
  // 显示请求时loading模态框 默认显示
  showLoading: true,
  // loading提醒文字
  loadingMsg: '加载中',
  // 需要授权才能请求 默认放开
  auth: false,
};

// Loading全局实例
let LoadingInstance = {
  target: null,
  count: 0,
};

/**
 * 关闭loading
 */
function closeLoading() {
  if (LoadingInstance.count > 0) LoadingInstance.count--;
  if (LoadingInstance.count === 0) uni.hideLoading();
}

// 处理token失效
function handleTokenExpired() {
  // const userStore = $store('user');
  const userStore = user();
  userStore.logout(true);
  // 清除本地存储的手机号
  uni.removeStorageSync('mobile');
  // 跳转到登录页
  uni.reLaunch({
    url: '/pages/login/login'
  });
}

/**
 * @description 请求基础配置 可直接使用访问自定义请求
 */
const http = new Request({
  baseURL:  baseUrl+apiModel,
  timeout: 15000,
  method: 'GET',
  // #ifdef APP-PLUS
  sslVerify: false,
  // #endif
  // #ifdef H5
  // 跨域请求时是否携带凭证（cookies）仅H5支持（HBuilderX 2.6.15+）
  withCredentials: false,
  // #endif
  custom: options,
});

/**
 * @description 请求拦截器
 */
http.interceptors.request.use(
  (config) => {
	  const userStore = user();
    if (config.custom.auth && !userStore.getIsLogin()) {
      console.log(" 这里做未登录拦截",userStore.getIsLogin())
      // 这里做未登录拦截
      handleTokenExpired();
      return Promise.reject();
    }
    if (config.custom.showLoading) {
      LoadingInstance.count++;
      LoadingInstance.count === 0 &&
        uni.showLoading({
          title: config.custom.loadingMsg,
          mask: true,
          fail: () => {
            uni.hideLoading();
          },
        });
    }
    // 判断本地是否存在token，如果存在则带上请求头
    let timestamp = Date.parse(new Date().toString())/1000;
    const passstr=md5(import.meta.env.GF_API_SECRET+timestamp)
    let baseheader = {
      Accept: 'text/json',
      'Businessid': import.meta.env.GF_BUSINESUSSID,
      // 'apiverify':window.btoa(passstr+"#"+timestamp),
      'apiverify': base64Encode(passstr+"#"+timestamp),
    };
    
    // 如果是文件上传，不设置Content-Type，让浏览器自动处理
	// #ifdef WEB
    if (config.data instanceof FormData) {
      config.header = {
        ...config.header,
        ...baseheader
      };
      // 删除手动设置的Content-Type，让浏览器自动处理
      delete config.header['Content-Type'];
    } else {
      config.header = {
        ...baseheader,
        'Content-Type': 'application/json;charset=UTF-8'
      };
    }
    // #endif
	// #ifndef WEB
	config.header = {
	  ...baseheader,
	  'Content-Type': 'application/json;charset=UTF-8'
	};
	// #endif
    const token = uni.getStorageSync('token');
    if (token) {
      config.header = {
        ...config.header,
        Authorization: `${token}`
      };
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

/**
 * @description 响应拦截器
 */
http.interceptors.response.use(
  (response) => {
    response.config.custom.showLoading && closeLoading();
    
    // 处理token失效的情况 - 不显示错误提示，直接跳转
    if (response.data.code === 401 || response.data.code === 'TOKEN_INVALID' || response.data.code === 'TOKEN_EXPIRED') {
      handleTokenExpired();
      return Promise.reject(response.data);
    }
    
    if (response.data.code !== 0 && response.config.custom.showError) {
		const d_message = response.data.message;
		const d_data = response.data.data;
		
		let message = '';
		if (d_data && d_message && d_message != d_data && typeof d_data == 'string') {
			message = d_message + ',' + d_data;
		}else if(d_message || (d_data && d_data == 'string')) {
			message = d_message || d_data;
		}else{
			message = '服务器开小差啦,请稍后再试~';
		}
        uni.showToast({
          title: message,
          icon: 'none',
          mask: true,
        });
      return Promise.resolve(response.data);
    }
    
    // 处理登录成功时的token和手机号存储
    if (response.data.data && response.data.data.token) {
      // const userStore = $store('user');
	  const userStore = user();
      userStore.setToken(response.data.data.token);
      // 如果返回数据中包含手机号，存储到本地
      if (response.data.data.mobile) {
        uni.setStorageSync('mobile', response.data.data.mobile);
      }
    }
    if (response.data.code === 0 && response.data.message !== '' && response.config.custom.showSuccess) {
      uni.showToast({
        title: response.config.custom.successMsg || response.data.message,
        icon: 'none',
      });
    }
    
    return Promise.resolve(response.data);
  },
  (error) => {
    // const userStore = $store('user');
	const userStore = user();
    const isLogin = userStore.isLogin;
    let errorMessage = '网络请求出错';
    
    if (error !== undefined) {
      switch (error.statusCode) {
        case 400:
          errorMessage = '请求错误';
          break;
        case 401:
          // token超时或无效，直接跳转登录页，不显示错误提示
          handleTokenExpired();
          return Promise.reject(error);
        case 403:
          errorMessage = '拒绝访问';
          break;
        case 404:
          errorMessage = '请求出错';
          break;
        case 408:
          errorMessage = '请求超时';
          break;
        case 429:
          errorMessage = '请求频繁, 请稍后再访问';
          break;
        case 500:
          errorMessage = '服务器开小差啦,请稍后再试~';
          break;
        case 501:
          errorMessage = '服务未实现';
          break;
        case 502:
          errorMessage = '网络错误';
          break;
        case 503:
          errorMessage = '服务不可用';
          break;
        case 504:
          errorMessage = '网络超时';
          break;
        case 505:
          errorMessage = 'HTTP版本不受支持';
          break;
      }
    }

    if (error && error.config) {
      // 如果是401错误，不显示错误提示
      if (error.statusCode === 401) {
        error.config.custom.showLoading && closeLoading();
        return Promise.reject(error);
      }
      
      if (error.config.custom.showError === false) {
        uni.showToast({
          title: error.data?.message || errorMessage,
          icon: 'none',
          mask: true,
        });
      }
      error.config.custom.showLoading && closeLoading();
    }

    return Promise.reject(error);
  },
);

const request = (config) => {
  if (config.url[0] !== '/') {
     config.url = apiPath + config.url;
  }
  return http.request(config);
};

export default request;
