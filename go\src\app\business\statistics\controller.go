package statistics

import (
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"reflect"

	"github.com/gin-gonic/gin"
)

// StatisticsController 统计控制器
type StatisticsController struct{}

func init() {
	controller := StatisticsController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// GetChannelStatistics 获取渠道统计列表
func (c *StatisticsController) GetChannelStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetChannelStatisticsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"date":      ctx.Query("date"),
		"page":      ctx.Query("page"),
		"page_size": ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetChannelStatisticsList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取渠道统计列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道统计列表成功", result, nil)
}
