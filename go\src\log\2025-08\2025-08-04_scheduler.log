{"level":"dev.info","ts":"[2025-08-04 11:15:36.329]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.331]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.331]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.331]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.331]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:15:36.332]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.616]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:20:56.617]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.858]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:07.860]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.665]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:26:16.667]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.231]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.232]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:38:06.233]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.589]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:39:53.591]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.611]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:40:29.612]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:41:40.950]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.773]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.774]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.774]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.774]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.774]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:43:56.775]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.392]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 11:53:14.396]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.597]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":0}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":0,"success":0,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.598]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.599]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":0,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.599]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":0}
{"level":"dev.info","ts":"[2025-08-04 14:06:38.599]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.632]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.633]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.633]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.633]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,24 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:55:51.634]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.error","ts":"[2025-08-04 14:55:51.634]","caller":"engine/scheduler.go:130","msg":"注册任务失败","task_name":"channel_statistics_task","error":"创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).registerAllTasks\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:130\nfincore/app/scheduler/engine.(*ScheduleEngine).Start\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:75\nfincore/app/scheduler.(*SchedulerManager).Start\n\tD:/work/code/fincore/go/src/app/scheduler/manager.go:97\nfincore/app/scheduler.Start\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:63\nmain.main\n\tD:/work/code/fincore/go/src/main.go:34\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.error","ts":"[2025-08-04 14:55:51.634]","caller":"scheduler/scheduler.go:64","msg":"启动调度器失败","error":"启动调度引擎失败: 注册任务失败: 创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"fincore/app/scheduler.Start\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:64\nmain.main\n\tD:/work/code/fincore/go/src/main.go:34\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.976]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.978]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.978]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.979]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.979]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,24 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:56:00.980]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.error","ts":"[2025-08-04 14:56:00.981]","caller":"engine/scheduler.go:130","msg":"注册任务失败","task_name":"channel_statistics_task","error":"创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).registerAllTasks\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:130\nfincore/app/scheduler/engine.(*ScheduleEngine).Start\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:75\nfincore/app/scheduler.(*SchedulerManager).Start\n\tD:/work/code/fincore/go/src/app/scheduler/manager.go:97\nfincore/app/scheduler.Start\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:63\nmain.main\n\tD:/work/code/fincore/go/src/main.go:34\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.error","ts":"[2025-08-04 14:56:00.981]","caller":"scheduler/scheduler.go:64","msg":"启动调度器失败","error":"启动调度引擎失败: 注册任务失败: 创建调度作业失败: gocron: CronJob: crontab parse failure\nend of range (24) above maximum (23): 24","stacktrace":"fincore/app/scheduler.Start\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:64\nmain.main\n\tD:/work/code/fincore/go/src/main.go:34\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.188]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.188]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.188]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.188]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.189]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:56:50.190]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.046]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.047]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.047]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.047]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.048]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.049]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:08.049]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.142]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.143]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.143]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.144]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.145]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.145]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.145]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:28.145]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.446]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.447]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:41.448]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.971]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.973]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:58:55.974]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.788]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.790]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:59:21.791]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.178]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.179]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.179]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.179]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.179]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.179]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 14:59:33.180]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.188]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.189]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.189]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.189]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:01:51.190]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.080]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.082]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.083]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.084]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.086]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.086]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.087]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:04.088]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.374]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.375]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.375]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.375]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.375]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.375]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:17.376]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.861]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.862]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.863]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.863]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.863]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:02:40.863]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.544]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.545]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.546]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:06:52.547]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.465]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.466]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.466]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.466]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:13:33.467]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.028]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.028]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.028]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.029]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.030]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.030]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.030]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:15:43.030]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.066]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.067]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:23:32.068]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.633]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.635]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.636]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.636]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.636]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:26:54.636]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.313]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.314]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.314]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.314]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:27:18.315]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.375]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.376]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.376]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.376]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:32:17.377]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.750]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.750]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.750]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.750]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.751]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:33:34.752]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.762]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:36:57.763]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.426]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.428]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.428]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.428]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.428]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:38:23.429]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.704]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.704]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.705]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:39:54.706]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.076]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.077]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:45:12.078]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.205]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.206]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.207]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.207]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.207]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 12,0 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-04 15:48:06.208]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.166]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.167]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.167]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.167]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.169]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.170]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.171]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.171]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.171]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:22:47.171]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.warn","ts":"[2025-08-04 16:22:50.105]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"channel_statistics_task","attempt":0,"max_retries":2,"error":"部分渠道统计失败，错误数量: 9"}
{"level":"dev.info","ts":"[2025-08-04 16:22:50.105]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"channel_statistics_task","attempt":1,"retry_interval":300}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.136]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.139]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.139]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.139]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.140]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.141]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:26:29.142]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:26:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.warn","ts":"[2025-08-04 16:26:52.752]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"channel_statistics_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.467]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.468]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.468]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.468]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.472]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.473]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.473]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.473]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.473]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.474]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.474]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:28:02.474]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:28:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.warn","ts":"[2025-08-04 16:28:29.283]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"channel_statistics_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.597]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.598]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.599]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.599]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.602]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.603]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.603]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:29:49.604]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:29:50.008]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:30:01.375]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.949]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.949]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.949]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.949]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.956]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:39:39.957]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.error","ts":"[2025-08-04 16:39:40.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"channel_statistics_task","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 50 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x1978120?, 0x2b7bfc0?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0x0)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x25\nfincore/utils/gform.NewSession({0x232aec8, 0x0})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x232aec8, 0x0})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0x0)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000df2bd8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/business/statistics.(*Repository).GetEnabledChannels(0xc000758160)\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:30 +0xe7\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics(0xc000b96120)\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:46 +0x497\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute(0xc000d7a510, {0x2329548, 0xc0001462a0})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58 +0x14a\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000d620c0, 0xc000c3e100)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000d620c0, 0xc000c3e100)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000d620c0, {0x232c6c0, 0xc000d7a510})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x33e\nreflect.Value.call({0x18e17e0, 0xc000d7a5d0, 0x13}, {0x1b1360c, 0x4}, {0x2c259e0, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x18e17e0, 0xc000d7a5d0, 0x13}, {0x2c259e0, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x18e17e0, 0xc000d7a5d0}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x23294d8, 0xc000cb84b0}, {0x23294d8, 0xc000cb9680}, 0xc000a8e5b0, {0xba, 0xdb, 0xa2, 0xba, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x23294d8, 0xc000cb84b0}, {0x23294d8, 0xc000cb9680}, 0xc000a8e5b0, {0xba, 0xdb, 0xa2, 0xba, 0xbf, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 33\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/business/statistics.(*Repository).GetEnabledChannels\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:30\nfincore/app/business/statistics.(*Service).ExecuteChannelStatistics\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:46\nfincore/app/scheduler/tasks/statistics.(*ChannelStatisticsTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/statistics/channel_statistics_task.go:58\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.002]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.0015685,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 16:39:40.002]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:39:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.652]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.652]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.653]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.653]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.653]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.653]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:42:29.654]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:42:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.334]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.335]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.335]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.335]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.337]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.337]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.337]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.337]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.339]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:43:04.340]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:43:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.215]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.216]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.216]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.216]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.218]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:44:50.219]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.032]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0325579,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 16:45:00.032]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 16:45:04.760]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":4.7603309,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 16:45:04.760]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.156]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.157]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.157]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.157]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.159]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:46:16.160]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:46:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.225]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.227]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.227]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.227]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.228]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:55:26.229]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:55:30.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.771]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.773]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.773]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.773]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.774]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.775]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 16:57:04.776]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 16:57:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.804]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.806]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.806]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.806]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.807]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.808]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.808]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.808]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.808]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.809]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.809]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 17:04:45.809]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.255329,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:04:50.257]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.033]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0332567,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.033]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0396521,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.039]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":4,"duration":0.2486291,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:05:00.248]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.228]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.229]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.229]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.229]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.230]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.231]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 17:06:17.232]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.1995397,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:06:20.200]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":2,"duration":0.2006637,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:06:30.201]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":3,"duration":0.1547798,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:06:40.154]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.116]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.119]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.119]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.119]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 17:09:30.121]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.209]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.2096797,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:09:40.210]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":2,"duration":0.1083219,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:09:50.108]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0105455,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.011]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0196537,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.020]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":3,"duration":0.152018,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.152]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.301]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":6,"duration":0.300385,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:10:00.301]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.962]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.963]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.963]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.963]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.964]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 17:19:56.966]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.032]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0311583,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.032]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0368826,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.037]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.0384563,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-04 17:20:00.039]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.627]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.627]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.627]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.627]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.632]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.632]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 19:44:50.633]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.237]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.239]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.239]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.239]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":4}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":4,"success":4,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.240]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":4,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":4}
{"level":"dev.info","ts":"[2025-08-04 19:45:04.242]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.530]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.531]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.531]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.532]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.533]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.534]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-04 19:45:37.535]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.608]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.610]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.610]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.610]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.610]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":5}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.610]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":5,"success":5,"failure":0}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.612]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":5,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":5}
{"level":"dev.info","ts":"[2025-08-04 20:12:06.613]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
