<template>
	<!-- 页面最外层容器，设置蓝白渐变背景 -->
	<div class="profile-page">
		<div :style="{ height: statusBarHeight + 'px' }"></div>
		<!-- 顶部信息区域 -->
		<div class="top-bar">
			<div class="avatar-info">
				<div class="avatar"></div>
				<p class="phone">{{ getShowMobile() }}</p>
			</div>
			<div class="top-icons">
				<!-- 通知图标和文字 -->
				<!-- <div class="icon-wrapper">
					<image src="/static/icon/tz.png" class="top-icon"></image>
					<span class="icon-text">通知</span>
				</div> -->
				<!-- 投诉图标和文字 -->
				<div class="icon-wrapper" @click="handleComplaint">
					<image src="/static/icon/gzts.png" class="top-icon"></image>
					<span class="icon-text">投诉</span>
				</div>
			</div>
		</div>
		<!-- 额度信息卡片/无借还记录前显示 -->
		<div class="credit-card" v-if="!repay.pending_loan">
			<div class="credit-card-row">
				<div class="credit-info">
					<template v-if="riskResult == -1 || riskResult == 0">
						<p class="card-subtitle">{{ riskResult == 0?'可用额度(元)':'可借额度(元)' }}</p>
						<p class="credit-amount">{{ riskResult == 0?availableCreditLimit:amountLimit}}</p>
					</template>
					<template v-else-if="riskResult == 1">
						<p class="card-subtitle">审核中</p>
						<p class="credit-amount">请耐心等待</p>
					</template>
					<template v-else>
						<p class="card-subtitle">综合评分不足</p>
						<p class="credit-amount">很遗憾审核未通过</p>
					</template>

					<p class="rate-desc">年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）</p>
				</div>
				<button class="get-money-btn" @click="handleGetMoney">马上拿钱</button>
			</div>
		</div>
		<!-- 借还记录&银行卡/有借还记录后显示 -->
		<view class="record-card" v-else>
			<view class="record-head">
				<image src="/static/image/zlyxz.png" mode="heightFix"></image>
				<text>完善资料可提升额度</text>
				<button @click="toCompInfo">去完善</button>
			</view>
			<view class="record-box">
				<view class="record-tab" @click="toRecordsPath">
					<view class="record-tab-icon">
						<image src="/static/image/record.png" mode="widthFix"></image>
					</view>
					<text>借还记录</text>
				</view>
				<view class="record-tab" @click="toCardPath">
					<view class="record-tab-icon">
						<image src="/static/image/card.png" mode="widthFix"></image>
					</view>
					<text>银行卡</text>
				</view>
			</view>
		</view>

		<!-- 功能入口区域 -->
		<div class="func-grid">
			<div class="func-item" @click="toCompInfo" v-if="userInfo.bizId">
				<image src="/static/icon/sfxx.png" class="func-icon"></image>
				<p class="func-desc">身份信息</p>
			</div>
			<div class="func-item" @click="funxi" v-if="!userInfo.bizId">
				<image src="/static/icon/smrz.png" class="func-icon"></image>
				<p class="func-desc">实名认证</p>
			</div>
			<div class="func-item" v-if="!repay.pending_loan" @click="toCardPath">
				<image src="/static/icon/card.png" class="func-icon"></image>
				<p class="func-desc">银行卡</p>
			</div>
			<div v-else class="func-item" @click="handleInvite">
				<image src="/static/icon/yqh.png" class="func-icon"></image>
				<p class="func-desc">邀请新用户</p>
			</div>
			<div class="func-item" @click="toMyBalance">
				<image src="/static/icon/kyhd.png" class="func-icon"></image>
				<p class="func-desc">我的额度</p>
			</div>
			<div class="func-item" @click="openServiceModal">
				<image src="/static/icon/kf1.png" class="func-icon"></image>
				<p class="func-desc">在线客服</p>
			</div>
		</div>
		<!-- 帮助中心、设置区域 -->
		<!-- <div class="help-setting-group">
			<div class="help-setting-single">
				<div class="help-center">帮助中心</div>
			</div>
			<div class="help-setting-single">
				<div class="setting" @click="goToSetting">设置</div>
			</div>
		</div> -->
		<view class="box-list">
			<view class="box-list-item" @click="handleInvite">
				<image src="/static/image/help.png" mode="heightFix" class="box-item-icon"></image>
				<text class="box-item-name">
					帮助中心
				</text>
				<image src="/static/image/more.png" mode="widthFix" class="box-item-more"></image>
			</view>
			<view class="box-list-item" @click="goToSetting">
				<image src="/static/image/setting.png" mode="heightFix" class="box-item-icon"></image>
				<text class="box-item-name">
					设置
				</text>
				<image src="/static/image/more.png" mode="widthFix" class="box-item-more"></image>
			</view>
		</view>

		<!-- 在线客服底部弹框 -->
		<div class="service-modal" v-if="showServiceModal" @click="closeServiceModal">
			<div class="modal-content" @click.stop>
				<div class="modal-header">
					<text class="modal-title" @click="handleCall()">电话客服: ************</text>
					<div class="modal-close" @click="closeServiceModal">
						取消
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {
		ref,
		reactive
	} from 'vue';
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app';
	import {
		getShowMobile as getShowMobileHelper
	} from '@/utils/helper';

	import user from '@/store/user.js';
	import {
		storeToRefs
	} from 'pinia';
	const userStore = user();
	import userApi from '@/api/user.js';

	const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
	// userInfo -- 用户信息
	// riskResult -- 是否审核通过 0 通过 1 审核 2 拒绝 3 调用风控模型失败
	// amountLimit 额度
	const {
		userInfo,
		isLogin,
		riskResult,
		amountLimit,
		availableCreditLimit,
		dayRate,
		cid
	} = storeToRefs(userStore);

	// 还款计划
	const repay = reactive({
		isData: false,
		days_to_next_due: 0,
		loan_total_amount: 0,
		repayment_schedule: [],
		pending_loan: false
	})

	onLoad(async () => {
		let uid = userInfo.value?.uid;
		if (!uid) {
			await userStore.getInfo();
			uid = userInfo.value?.uid;
		}
		if (userInfo.value.identityStatus && userInfo.value.identityStatus == 2) { // 实名认证审核通过后再获取
			// 更新贷款是否审核通过状态
			await userStore.getEvaluate(uid);
			// 获取授信额度&贷款产品
			await userStore.getProducts(uid);

			// 获取还款计划
			const orderBills = await userApi.getOrderBills({});
			if (orderBills.code == 0) {
				repay.days_to_next_due = orderBills.data.days_to_next_due || 0;
				repay.loan_total_amount = orderBills.data.loan_total_amount || 0;
				repay.repayment_schedule = orderBills.data.repayment_schedule || [];
				repay.pending_loan = orderBills.data.pending_loan;
				if(orderBills.data.loan_total_amount > 0 && orderBills.data.repayment_schedule.length > 0) {
					repay.isData = true;
				}
			}
		}
	});

	// 控制客服弹框显示状态
	const showServiceModal = ref(false);

	// 打开客服弹框
	const openServiceModal = () => {
		showServiceModal.value = true;
	};

	// 关闭客服弹框
	const closeServiceModal = () => {
		showServiceModal.value = false;
	};

	function handleCall() {
		uni.makePhoneCall({
			phoneNumber: '************'
		})
	}

	// 新增：处理投诉点击
	const handleComplaint = () => {

		userStore.nextCallBack().then(res => {
			// 这里写跳转投诉页面逻辑
			uni.navigateTo({
				url: '/pages/index/complaint'
			});
		}).catch(err => {
			if (err.code == 2) {
				uni.showToast({
					title: "请先完成实名认证",
					icon: "none"
				})
			}
			if (err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});
	};

	// 跳转到设置页
	const goToSetting = () => {
		uni.navigateTo({
			url: "/pages/profile/SettingPage"
		});
	};


	// 页面跳转函数
	function funxi() {
		if (!cid.value) {
			return uni.showToast({
				title: "渠道信息不存在",
				icon: "none"
			})
		}
		uni.navigateTo({
			url: "/pages/Authentication/Authentication",
		});
	}

	function toCardPath() {
		if (repay.pending_loan) {
			uni.navigateTo({
				url: "/pages/CollectionCard/BankCardPage",
			});
		} else {
			userStore.nextCallBack().then(res => {
				uni.navigateTo({
					url: "/pages/CollectionCard/BankCardPage",
				});
			}).catch(err => {
				if (err.code == 2) {
					uni.showToast({
						title: "请先完成实名认证",
						icon: "none"
					})
				}
				if (err.message) {
					uni.showToast({
						title: err.message,
						icon: "none"
					})
				}
			});
		}

	}

	function toCompInfo() {
		uni.navigateTo({
			url: "/pages/FaceRecognition/FaceRecognition?type=edit"
		})

	}

	function toRecordsPath() {
		if (repay.pending_loan) {
			uni.navigateTo({
				url: "/pages/profile/records"
			})
		}else{
			userStore.nextCallBack().then(res => {
				uni.navigateTo({
					url: "/pages/profile/records"
				})
			}).catch(err => {
				if (err.code == 2) {
					uni.showToast({
						title: "请先完成实名认证",
						icon: "none"
					})
				}
				if (err.message) {
					uni.showToast({
						title: err.message,
						icon: "none"
					})
				}
			});
		}
		

	}

	function handleInvite() {
		uni.showToast({
			title: '开发中...',
			icon: 'none'
		})
	}

	function toMyBalance() {
		userStore.nextCallBack().then(res => {
			uni.navigateTo({
				url: "/pages/profile/CreditLimit",
			});
		}).catch(err => {
			if (err.code == 2) {
				uni.showToast({
					title: "请先完成实名认证",
					icon: "none"
				})
			}
			if (err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});
	}

	// 获取显示的手机号 - 使用统一工具函数
	function getShowMobile() {
		return getShowMobileHelper(userStore);
	}

	// 检查登录状态
	function checkLogin() {
		if (!userStore.isLogin) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			}, 1500);
			return false;
		}
		return true;
	}
	// 马上拿钱按钮点击
	function handleGetMoney() {
		if (!checkLogin()) return;
		userStore.nextCallBack().then(res => {
			uni.navigateTo({
				url: "/pages/BorrowMoney/BorrowMoney",
			});
		}).catch(err => {
			if (err.url) {
				uni.navigateTo({
					url: err.url
				});
			}
			if (err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});
	}
</script>


<style scoped lang="scss">
	/* 页面整体样式，蓝白垂直渐变背景 */
	.profile-page {
		width: 100%;
		min-height: 100vh;
		background: linear-gradient(to bottom, #1a6eff 20%, #eff2f7 45%);
		padding: 16px;
		box-sizing: border-box;
	}

	/* 顶部信息栏布局 */
	.top-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 35px;
	}

	.avatar-info {
		display: flex;
		align-items: center;
	}

	.avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: #fff;
		margin-right: 8px;
	}

	.phone {
		color: #fff;
		font-size: 16px;
	}

	/* 顶部图标区域 - 优化为上下布局 */
	.top-icons {
		display: flex;
		gap: 16px;
	}

	.icon-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.top-icon {
		width: 24px;
		height: 24px;
		margin-bottom: 4px;
	}

	.icon-text {
		color: #fff;
		font-size: 12px;
	}

	/* 额度卡片样式 */
	.credit-card {
		background: rgba(255, 255, 255, .9);
		border-radius: 8px;
		padding: 30rpx 48rpx;
		color: rgb(1, 114, 255);
		margin-bottom: 30rpx;
	}

	.credit-card-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.credit-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.card-subtitle {
		font-size: 13px;
		color: rgb(1, 114, 255);
		margin-bottom: 3px;
		font-weight: 500;
	}

	.credit-amount {
		font-size: 40rpx;
		color: rgb(1, 114, 255);
		font-weight: 600;
		margin-bottom: 8px;
		margin-top: 0;
	}

	.rate-desc {
		font-size: 10px;
		color: rgb(10, 137, 254);
		white-space: nowrap;
		margin-top: 2px;
	}

	.get-money-btn {
		width: 180rpx;
		flex-shrink: 0;
		height: 80rpx;
		line-height: 80rpx;
		background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
		color: #fff;
		border: none;
		border-radius: 22px;
		font-size: 13px;
		cursor: pointer;
		margin-right: 0;
		margin-top: 0;
		padding: 0;
		box-sizing: border-box;
		text-align: center;
		white-space: nowrap;
	}

	/* 功能入口网格布局 */
	.func-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
		background: rgba(255, 255, 255, .9);
		border-radius: 15rpx;
		padding: 16px;
		box-sizing: border-box;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
	}

	.func-item {
		text-align: center;
	}

	.func-icon {
		width: 30px;
		height: 30px;
		margin-bottom: 4px;
	}

	.func-desc {
		font-size: 12px;
		color: #999;
	}

	/* 帮助中心、设置区域样式 */
	.help-setting-group {
		display: flex;
		flex-direction: column;
		gap: 12px;
	}

	.help-setting-single {
		background-color: #fff;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		padding: 12px 16px;
		box-sizing: border-box;
	}

	.help-center,
	.setting {
		font-size: 14px;
		color: #333;
		padding: 8px 0;
		border-bottom: none;
	}

	/* 客服弹框样式 */
	.service-modal {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: flex-end;
		align-items: flex-end;
		z-index: 999;
	}

	.modal-content {
		width: 100%;
		height: 110px;
		background-color: #fff;
		overflow: hidden;
		position: relative;
		box-shadow: none;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 24px;
	}

	.modal-header {
		width: 100%;
		text-align: center;
		padding: 0;
		border-bottom: none;
	}

	.modal-title {
		display: block;
		font-size: 16px;
		font-weight: normal;
		color: #333;
		margin-bottom: 24px;
		margin-top: 0;
		line-height: 1.5;
	}

	.modal-close {
		margin-top: 10px;
		font-size: 14px;
		color: #bbb;
		cursor: pointer;
		text-align: center;
		font-weight: normal;
	}

	.modal-body {
		padding: 15px;
		font-size: 14px;
		line-height: 1.6;
	}

	.service-item {
		display: flex;
		align-items: center;
		padding: 12px 0;
		border-bottom: 1px solid #f5f5f5;
	}

	.service-item:last-child {
		border-bottom: none;
	}

	.service-icon {
		width: 24px;
		height: 24px;
		margin-right: 12px;
	}

	.service-text {
		color: #333;
	}

	.box-list {
		background: rgba(255, 255, 255, .8);
		border-radius: 15rpx;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.box-list-item {
			padding: 30rpx;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e5e5e5;

			&:last-child {
				border-bottom: 0;
			}

			.box-item-icon {
				height: 30rpx;
			}

			.box-item-name {
				flex: 1;
				margin: 0 20rpx;
			}

			.box-item-more {
				width: 40rpx;
			}
		}
	}

	.record-card {
		background: rgba(255, 255, 255, .9);
		border-radius: 16rpx;
		padding: 3rpx;
		color: rgb(1, 114, 255);
		margin-bottom: 30rpx;
		overflow: hidden;

		.record-head {
			background-color: #1a6eff;
			background-image: radial-gradient(#1E90FF 2px, transparent 2px);
			padding: 20rpx 30rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx 16rpx 0 0;

			image {
				height: 40rpx;
			}

			text {
				flex: 1;
				color: #aec8e1;
				margin: 0 20rpx;
			}

			button {
				height: 50rpx;
				line-height: 50rpx;
				padding: 0 20rpx;
				background: #eee;
				color: #1E90FF;
				border: none;
				border-radius: 30rpx;
				font-size: 24rpx;
				cursor: pointer;
				margin-right: 0;
				margin-top: 0;
				box-sizing: border-box;
				text-align: center;
				white-space: nowrap;

			}
		}

		.record-box {
			display: flex;
			justify-content: space-between;
			padding: 20rpx;
		}

		.record-tab {
			flex: 1;
			background-color: #eee;
			margin-left: 20rpx;
			padding: 30rpx 20rpx;
			display: flex;
			align-items: center;
			border-radius: 10rpx;

			&:first-child {
				margin-left: 0;
			}

			.record-tab-icon {
				width: 60rpx;
				height: 60rpx;
				background: rgba(249, 132, 52, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;

				image {
					width: 35rpx;
				}
			}

			text {
				margin-left: 25rpx;
				color: #333;
				font-size: 32rpx;
			}
		}
	}
</style>